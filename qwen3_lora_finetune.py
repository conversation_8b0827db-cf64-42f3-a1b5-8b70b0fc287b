#!/usr/bin/env python3
"""
Optimized Qwen3-1.7B LoRA Fine-tuning with AMS Data for Tesla P40
Using LoRA for efficient training with minimal VRAM usage
"""
import os
import sys
import torch
import logging
import json
import pickle
import subprocess
from datetime import datetime
from pathlib import Path
from transformers import AutoModelForCausalLM, AutoTokenizer, Trainer, TrainingArguments
from torch.utils.data import Dataset
from peft import LoraConfig, get_peft_model, TaskType

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[logging.FileHandler('lora_finetune_log.txt'), logging.StreamHandler(sys.stdout)]
)
logger = logging.getLogger(__name__)

def check_gpu():
    """Check GPU availability and VRAM"""
    try:
        import pynvml
        pynvml.nvmlInit()
        device = pynvml.nvmlDeviceGetHandleByIndex(0)
        mem = pynvml.nvmlDeviceGetMemoryInfo(device)
        gpu_name = pynvml.nvmlDeviceGetName(device)
        if isinstance(gpu_name, bytes):
            gpu_name = gpu_name.decode()
        logger.info(f"GPU: {gpu_name} | Free VRAM: {mem.free / 1024 / 1024:.1f} MB")
        return mem.free / 1024 / 1024
    except Exception as e:
        logger.error(f"GPU check failed: {e}")
        try:
            if torch.cuda.is_available():
                gpu_name = torch.cuda.get_device_name(0)
                free_mem = torch.cuda.get_device_properties(0).total_memory / 1024 / 1024
                logger.info(f"GPU (torch): {gpu_name} | Total VRAM: {free_mem:.1f} MB")
                return free_mem * 0.8
        except:
            pass
        return 0

def load_ams_data(data_dir="./data", cache_file="data_cache.pkl"):
    """Load and cache AMS data from data folder"""
    logger.info("📚 Loading AMS data...")
    if os.path.exists(cache_file):
        with open(cache_file, 'rb') as f:
            data = pickle.load(f)
        logger.info(f"Loaded cached data: {len(data)} samples")
        return data

    data_path = Path(data_dir)
    if not data_path.exists():
        logger.error("Data directory not found!")
        return []

    all_text = []
    for txt_file in data_path.glob("*.txt"):
        try:
            with open(txt_file, 'r', encoding='utf-8') as f:
                content = f.read()
                all_text.append(content)
                logger.info(f"Loaded {txt_file.name}: {len(content)} characters")
        except Exception as e:
            logger.error(f"Error reading {txt_file}: {e}")

    # Enhanced QnA pairs from AMS data
    qna_pairs = [
        "Câu hỏi: Công ty AMS có địa chỉ ở đâu? Trả lời: Công ty AMS có trụ sở chính tại Nhà số 10, ngõ 7, phố Nguyễn Khả Trạc, phường Mai Dịch, quận Cầu Giấy, Hà Nội.",
        "Câu hỏi: Thời gian thử việc tại AMS là bao lâu? Trả lời: Thời gian thử việc là 60 ngày, có thể lên đến 90 ngày đối với cấp quản lý.",
        "Câu hỏi: Thu nhập thử việc như thế nào? Trả lời: Thu nhập thử việc tối thiểu 85% thu nhập chính thức.",
        "Câu hỏi: Nhân viên được nghỉ những ngày lễ nào? Trả lời: Nhân viên được nghỉ 5 ngày lễ: Tết Dương lịch, Giỗ tổ Hùng Vương, Ngày Giải phóng, Ngày Quốc tế Lao động, Ngày Quốc khánh.",
        "Câu hỏi: Quy định về trang phục tại AMS? Trả lời: Nhân viên cần ăn mặc lịch sự, phù hợp với môi trường công sở.",
        "Câu hỏi: Điện thoại công ty AMS là gì? Trả lời: Điện thoại công ty AMS là (84-24) 3795 66 22.",
        "Câu hỏi: Website của AMS là gì? Trả lời: Website của AMS là http://ams.net.vn.",
        "Câu hỏi: Email liên hệ AMS? Trả lời: Email liên hệ AMS là <EMAIL>.",
        "Câu hỏi: Đánh giá thử việc dựa trên gì? Trả lời: Đánh giá thử việc dựa trên năng lực, đạo đức và khả năng hòa nhập.",
        "Câu hỏi: Chính sách tiết kiệm tại AMS? Trả lời: Nhân viên cần tiết kiệm chi phí khi sử dụng văn phòng phẩm và thiết bị công ty.",
        "Câu hỏi: Giờ làm việc của AMS? Trả lời: Giờ làm việc từ 8h30 đến 17h30, nghỉ trưa từ 12h đến 13h.",
        "Câu hỏi: Chế độ bảo hiểm tại AMS? Trả lời: Công ty đóng đầy đủ bảo hiểm xã hội, bảo hiểm y tế, bảo hiểm thất nghiệp theo quy định.",
        "Câu hỏi: Quy trình nghỉ phép tại AMS? Trả lời: Nhân viên cần đăng ký nghỉ phép trước ít nhất 1 ngày và được phê duyệt bởi quản lý trực tiếp.",
        "Câu hỏi: Chính sách đào tạo tại AMS? Trả lời: Công ty có chương trình đào tạo định kỳ và hỗ trợ nhân viên tham gia các khóa học nâng cao.",
        "Câu hỏi: Quy định về an toàn lao động? Trả lời: Nhân viên phải tuân thủ nghiêm ngặt các quy định về an toàn lao động và sử dụng đầy đủ thiết bị bảo hộ."
    ]
    all_text.extend(qna_pairs)

    with open(cache_file, 'wb') as f:
        pickle.dump(all_text, f)
    logger.info(f"Cached {len(all_text)} samples to {cache_file}")
    return all_text

class AMSDataset(Dataset):
    def __init__(self, texts, tokenizer, max_length=512):
        self.texts = texts
        self.tokenizer = tokenizer
        self.max_length = max_length

    def __len__(self):
        return len(self.texts)

    def __getitem__(self, idx):
        text = self.texts[idx]
        encoding = self.tokenizer(
            text, truncation=True, padding='max_length',
            max_length=self.max_length, return_tensors='pt'
        )
        return {
            'input_ids': encoding['input_ids'].flatten(),
            'attention_mask': encoding['attention_mask'].flatten(),
            'labels': encoding['input_ids'].flatten()
        }

def setup_lora_model(model, lora_config):
    """Setup LoRA model with optimized configuration"""
    logger.info("🔧 Setting up LoRA configuration...")

    # Apply LoRA to the model
    lora_model = get_peft_model(model, lora_config)

    # Print trainable parameters
    trainable_params = sum(p.numel() for p in lora_model.parameters() if p.requires_grad)
    total_params = sum(p.numel() for p in lora_model.parameters())

    logger.info(f"✅ LoRA setup complete:")
    logger.info(f"  Trainable parameters: {trainable_params:,}")
    logger.info(f"  Total parameters: {total_params:,}")
    logger.info(f"  Trainable ratio: {100 * trainable_params / total_params:.2f}%")

    return lora_model

def fine_tune_qwen_lora():
    """Fine-tune Qwen3-1.7B with LoRA"""
    logger.info("🚀 Starting LoRA fine-tuning...")
    try:
        # Check GPU
        free_vram = check_gpu()
        if free_vram < 3000:  # LoRA needs much less VRAM
            logger.error("Insufficient VRAM for LoRA fine-tuning!")
            return False

        # Load model and tokenizer
        model_name = "Qwen/Qwen3-1.7B"  # Use Qwen3-1.7B as requested
        logger.info(f"📦 Loading model: {model_name}")

        tokenizer = AutoTokenizer.from_pretrained(model_name, trust_remote_code=True)
        if tokenizer.pad_token is None:
            tokenizer.pad_token = tokenizer.eos_token

        # Load model with aggressive memory optimization for Qwen3-1.7B
        model = AutoModelForCausalLM.from_pretrained(
            model_name,
            torch_dtype=torch.float16,
            device_map="auto",
            trust_remote_code=True,
            low_cpu_mem_usage=True,
            max_memory={0: "18GB"}  # Limit to 18GB for Tesla P40
        )

        logger.info(f"✅ Base model loaded. Parameters: {sum(p.numel() for p in model.parameters()):,}")

        # LoRA configuration optimized for Tesla P40
        lora_config = LoraConfig(
            task_type=TaskType.CAUSAL_LM,
            r=16,  # Rank - balance between performance and efficiency
            lora_alpha=32,  # Scaling factor
            lora_dropout=0.1,  # Dropout for regularization
            target_modules=[
                "q_proj", "k_proj", "v_proj", "o_proj",  # Attention layers
                "gate_proj", "up_proj", "down_proj"  # MLP layers
            ],
            bias="none",  # Don't adapt bias
            inference_mode=False,
        )

        # Setup LoRA model
        lora_model = setup_lora_model(model, lora_config)

        # Load data
        qna_data = load_ams_data()
        if not qna_data:
            logger.error("No training data available!")
            return False

        # Create dataset
        dataset = AMSDataset(qna_data, tokenizer, max_length=512)
        logger.info(f"✅ Dataset created with {len(dataset)} samples")

        # LoRA training arguments - ultra conservative for Qwen3-1.7B
        batch_size = 1  # Always use batch size 1 for Qwen3-1.7B
        grad_acc = 16  # Higher gradient accumulation

        training_args = TrainingArguments(
            output_dir="./qwen3_lora_finetuned",
            num_train_epochs=3,  # Reduced epochs
            per_device_train_batch_size=batch_size,
            gradient_accumulation_steps=grad_acc,
            learning_rate=2e-4,  # LoRA learning rate
            weight_decay=0.01,
            warmup_steps=10,
            logging_steps=5,
            save_steps=25,
            max_steps=100,  # Reduced steps
            fp16=False,  # Disable FP16 to avoid gradient scaling issues
            dataloader_num_workers=0,
            remove_unused_columns=False,
            report_to="none",
            save_total_limit=2,
            seed=42,
            gradient_checkpointing=False,  # Disable to avoid gradient issues
        )

        # Create trainer
        trainer = Trainer(
            model=lora_model,
            args=training_args,
            train_dataset=dataset,
            tokenizer=tokenizer
        )

        # Train
        logger.info("🎯 Starting LoRA fine-tuning...")
        trainer.train()

        # Save LoRA adapters
        lora_model_path = "./qwen3_lora_adapters"
        lora_model.save_pretrained(lora_model_path)
        tokenizer.save_pretrained(lora_model_path)

        logger.info(f"✅ LoRA adapters saved to: {lora_model_path}")

        # Test model
        logger.info("🧪 Testing LoRA fine-tuned model...")
        test_questions = [
            "Câu hỏi: Công ty AMS có địa chỉ ở đâu? Trả lời:",
            "Câu hỏi: Thời gian thử việc là bao lâu? Trả lời:",
            "Câu hỏi: Nhân viên được nghỉ những ngày lễ nào? Trả lời:",
            "Câu hỏi: Giờ làm việc của AMS? Trả lời:",
            "Câu hỏi: Chế độ bảo hiểm tại AMS? Trả lời:"
        ]

        lora_model.eval()
        test_results = []

        for i, test_input in enumerate(test_questions):
            try:
                inputs = tokenizer(test_input, return_tensors="pt").to(lora_model.device)

                with torch.no_grad():
                    outputs = lora_model.generate(
                        **inputs,
                        max_new_tokens=100,
                        do_sample=True,
                        temperature=0.3,
                        top_p=0.9,
                        pad_token_id=tokenizer.eos_token_id,
                        eos_token_id=tokenizer.eos_token_id
                    )

                response = tokenizer.decode(outputs[0], skip_special_tokens=True)
                logger.info(f"📝 Test {i+1}: {response}")
                test_results.append({'question': test_input, 'response': response})

            except Exception as e:
                logger.warning(f"⚠️ Test {i+1} failed: {e}")

        with open('./lora_test_results.json', 'w', encoding='utf-8') as f:
            json.dump(test_results, f, ensure_ascii=False, indent=2)

        torch.cuda.empty_cache()
        logger.info("🎉 LoRA fine-tuning completed successfully!")
        return lora_model_path

    except Exception as e:
        logger.error(f"❌ LoRA fine-tuning failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def merge_and_save_model(lora_model_path, base_model_name):
    """Merge LoRA adapters with base model and save"""
    logger.info("🔄 Merging LoRA adapters with base model...")

    try:
        from peft import PeftModel

        # Load base model
        base_model = AutoModelForCausalLM.from_pretrained(
            base_model_name,
            torch_dtype=torch.float16,
            device_map="auto",
            trust_remote_code=True
        )

        # Load LoRA model
        lora_model = PeftModel.from_pretrained(base_model, lora_model_path)

        # Merge adapters
        merged_model = lora_model.merge_and_unload()

        # Save merged model
        merged_model_path = "./qwen3_ams_merged"
        merged_model.save_pretrained(merged_model_path)

        # Save tokenizer
        tokenizer = AutoTokenizer.from_pretrained(base_model_name, trust_remote_code=True)
        tokenizer.save_pretrained(merged_model_path)

        logger.info(f"✅ Merged model saved to: {merged_model_path}")
        return merged_model_path

    except Exception as e:
        logger.error(f"❌ Model merging failed: {e}")
        return False

def convert_to_ollama(model_path):
    """Convert merged model to Ollama format"""
    logger.info(f"🔄 Converting model to Ollama format: {model_path}")

    try:
        modelfile_content = f"""FROM {model_path}
TEMPLATE "Câu hỏi: {{{{ .Prompt }}}}
Trả lời: "
PARAMETER temperature 0.3
PARAMETER top_p 0.9
PARAMETER stop "Câu hỏi:"
SYSTEM "Bạn là trợ lý AI của công ty AMS, được fine-tune với LoRA để trả lời chính xác các câu hỏi về quy định công ty bằng tiếng Việt."
"""

        with open('./Modelfile', 'w', encoding='utf-8') as f:
            f.write(modelfile_content)

        model_name = "qwen3-ams-lora"
        cmd = f"ollama create {model_name} -f ./Modelfile"

        logger.info(f"Running: {cmd}")
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True)

        if result.returncode == 0:
            logger.info(f"✅ Successfully created Ollama model: {model_name}")

            # Test the new model
            test_cmd = f'ollama run {model_name} "Công ty AMS có địa chỉ ở đâu?"'
            test_result = subprocess.run(test_cmd, shell=True, capture_output=True, text=True)

            if test_result.returncode == 0:
                logger.info(f"🧪 Test response: {test_result.stdout}")

            return model_name
        else:
            logger.error(f"❌ Failed to create Ollama model: {result.stderr}")
            return False

    except Exception as e:
        logger.error(f"❌ Error converting to Ollama: {e}")
        return False

def main():
    logger.info("🚀 Starting LoRA Fine-tuning for Qwen3 with AMS Data")

    # Step 1: LoRA fine-tuning
    lora_model_path = fine_tune_qwen_lora()
    if not lora_model_path:
        logger.error("❌ LoRA fine-tuning failed!")
        return False

    # Step 2: Merge LoRA with base model
    base_model_name = "Qwen/Qwen3-1.7B"
    merged_model_path = merge_and_save_model(lora_model_path, base_model_name)
    if not merged_model_path:
        logger.error("❌ Model merging failed!")
        return False

    # Step 3: Convert to Ollama
    ollama_model = convert_to_ollama(merged_model_path)
    if not ollama_model:
        logger.error("❌ Ollama conversion failed!")
        return False

    logger.info(f"🎉 Complete! LoRA fine-tuned model deployed as: {ollama_model}")
    logger.info(f"Usage: ollama run {ollama_model} 'Your question about AMS'")
    logger.info("📊 LoRA Benefits:")
    logger.info("  - 10-100x fewer trainable parameters")
    logger.info("  - 3-5x faster training")
    logger.info("  - 50-70% less VRAM usage")
    logger.info("  - Better generalization")

    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
