#!/usr/bin/env python3
"""
Script test toàn bộ pipeline và model đã deploy
"""
import os
import sys
import requests
import json
import logging
import subprocess
from typing import List, Dict

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Test configuration
OLLAMA_API_URL = "http://**********:11434"  # Remote Ollama server
LOCAL_OLLAMA_URL = "http://localhost:11434"   # Local Ollama server
MODEL_NAME = "qwen3-ams-lora"

def test_ollama_connection(api_url):
    """Test kết nối đến Ollama API"""
    try:
        response = requests.get(f"{api_url}/api/tags", timeout=10)
        if response.status_code == 200:
            models = response.json().get('models', [])
            model_names = [model['name'] for model in models]
            logger.info(f"✅ Connected to Ollama at {api_url}")
            logger.info(f"Available models: {model_names}")
            return model_names
        else:
            logger.error(f"❌ Failed to connect to {api_url}: {response.status_code}")
            return None
    except Exception as e:
        logger.error(f"❌ Connection error to {api_url}: {e}")
        return None

def test_model_inference(api_url, model_name, questions):
    """Test model inference với các câu hỏi"""
    logger.info(f"🧪 Testing model {model_name} at {api_url}")
    
    results = []
    for i, question in enumerate(questions, 1):
        try:
            logger.info(f"Question {i}: {question}")
            
            response = requests.post(
                f"{api_url}/api/generate",
                json={
                    "model": model_name,
                    "prompt": question,
                    "stream": False,
                    "options": {
                        "temperature": 0.3,
                        "top_p": 0.9,
                        "num_predict": 200
                    }
                },
                timeout=60
            )
            
            if response.status_code == 200:
                result = response.json()
                answer = result.get('response', '').strip()
                logger.info(f"Answer {i}: {answer}")
                
                results.append({
                    'question': question,
                    'answer': answer,
                    'status': 'success'
                })
            else:
                logger.error(f"❌ API error for question {i}: {response.status_code}")
                results.append({
                    'question': question,
                    'answer': f"API Error: {response.status_code}",
                    'status': 'error'
                })
                
        except Exception as e:
            logger.error(f"❌ Error testing question {i}: {e}")
            results.append({
                'question': question,
                'answer': f"Error: {str(e)}",
                'status': 'error'
            })
    
    return results

def test_local_files():
    """Test các file được tạo trong pipeline"""
    logger.info("📁 Testing generated files...")
    
    expected_files = [
        "generated_qna_data.json",
        "training_data.json",
        "qwen3_lora_adapters",
        "qwen3_ams_merged",
        "qwen3_ams_merged.gguf"
    ]
    
    file_status = {}
    for file_path in expected_files:
        if os.path.exists(file_path):
            if os.path.isfile(file_path):
                size = os.path.getsize(file_path) / (1024 * 1024)  # MB
                file_status[file_path] = f"✅ {size:.1f} MB"
            else:
                # Directory
                file_status[file_path] = "✅ Directory exists"
        else:
            file_status[file_path] = "❌ Not found"
    
    return file_status

def test_training_data_quality():
    """Test chất lượng dữ liệu training"""
    logger.info("📊 Testing training data quality...")
    
    try:
        # Test generated QnA data
        if os.path.exists("generated_qna_data.json"):
            with open("generated_qna_data.json", 'r', encoding='utf-8') as f:
                qna_data = json.load(f)
            
            logger.info(f"Generated QnA pairs: {len(qna_data)}")
            
            # Analyze data quality
            sources = {}
            for item in qna_data:
                source = item.get('source_file', 'unknown')
                sources[source] = sources.get(source, 0) + 1
            
            logger.info("Data distribution by source:")
            for source, count in sources.items():
                logger.info(f"  - {source}: {count} pairs")
            
            return {
                'total_pairs': len(qna_data),
                'sources': sources,
                'status': 'success'
            }
        else:
            return {'status': 'error', 'message': 'QnA data file not found'}
            
    except Exception as e:
        return {'status': 'error', 'message': str(e)}

def run_comprehensive_test():
    """Chạy test toàn diện"""
    logger.info("🚀 Starting Comprehensive Pipeline Test")
    logger.info("=" * 60)
    
    test_results = {
        'files': {},
        'training_data': {},
        'model_tests': {},
        'overall_status': 'unknown'
    }
    
    # Test 1: Local files
    logger.info("📁 TEST 1: Local Files")
    test_results['files'] = test_local_files()
    for file_path, status in test_results['files'].items():
        logger.info(f"  {file_path}: {status}")
    
    # Test 2: Training data quality
    logger.info("\n📊 TEST 2: Training Data Quality")
    test_results['training_data'] = test_training_data_quality()
    if test_results['training_data']['status'] == 'success':
        logger.info(f"  Total QnA pairs: {test_results['training_data']['total_pairs']}")
        for source, count in test_results['training_data']['sources'].items():
            logger.info(f"  {source}: {count} pairs")
    else:
        logger.error(f"  ❌ {test_results['training_data']['message']}")
    
    # Test 3: Model inference
    logger.info("\n🧪 TEST 3: Model Inference")
    
    test_questions = [
        "Công ty AMS có địa chỉ ở đâu?",
        "Thời gian thử việc tại AMS là bao lâu?",
        "Giờ làm việc của AMS như thế nào?",
        "Chế độ bảo hiểm tại AMS?",
        "Quy định về nghỉ phép tại AMS?"
    ]
    
    # Test remote Ollama first
    remote_models = test_ollama_connection(OLLAMA_API_URL)
    if remote_models and MODEL_NAME in remote_models:
        logger.info(f"Testing remote Ollama at {OLLAMA_API_URL}")
        test_results['model_tests']['remote'] = test_model_inference(
            OLLAMA_API_URL, MODEL_NAME, test_questions
        )
    else:
        logger.warning(f"⚠️ Model {MODEL_NAME} not found on remote Ollama")
        test_results['model_tests']['remote'] = {'status': 'model_not_found'}
    
    # Test local Ollama
    local_models = test_ollama_connection(LOCAL_OLLAMA_URL)
    if local_models and MODEL_NAME in local_models:
        logger.info(f"Testing local Ollama at {LOCAL_OLLAMA_URL}")
        test_results['model_tests']['local'] = test_model_inference(
            LOCAL_OLLAMA_URL, MODEL_NAME, test_questions
        )
    else:
        logger.warning(f"⚠️ Model {MODEL_NAME} not found on local Ollama")
        test_results['model_tests']['local'] = {'status': 'model_not_found'}
    
    # Overall status
    files_ok = all('✅' in status for status in test_results['files'].values())
    training_ok = test_results['training_data']['status'] == 'success'
    model_ok = (
        test_results['model_tests'].get('remote', {}).get('status') != 'model_not_found' or
        test_results['model_tests'].get('local', {}).get('status') != 'model_not_found'
    )
    
    if files_ok and training_ok and model_ok:
        test_results['overall_status'] = 'success'
    elif files_ok and training_ok:
        test_results['overall_status'] = 'partial'
    else:
        test_results['overall_status'] = 'failed'
    
    return test_results

def generate_test_report(test_results):
    """Tạo báo cáo test"""
    report = f"""# Pipeline Test Report

Generated: {__import__('datetime').datetime.now().isoformat()}

## Overall Status: {test_results['overall_status'].upper()}

## File Status
"""
    
    for file_path, status in test_results['files'].items():
        report += f"- {file_path}: {status}\n"
    
    report += f"""
## Training Data Quality
- Status: {test_results['training_data']['status']}
"""
    
    if test_results['training_data']['status'] == 'success':
        report += f"- Total QnA pairs: {test_results['training_data']['total_pairs']}\n"
        report += "- Source distribution:\n"
        for source, count in test_results['training_data']['sources'].items():
            report += f"  - {source}: {count} pairs\n"
    
    report += "\n## Model Inference Tests\n"
    
    for location, results in test_results['model_tests'].items():
        report += f"\n### {location.title()} Ollama\n"
        if isinstance(results, list):
            for i, result in enumerate(results, 1):
                report += f"\n**Question {i}:** {result['question']}\n"
                report += f"**Answer:** {result['answer']}\n"
                report += f"**Status:** {result['status']}\n"
        else:
            report += f"Status: {results.get('status', 'unknown')}\n"
    
    return report

def main():
    """Main function"""
    logger.info("🎯 Qwen3 LoRA Pipeline Comprehensive Test")
    
    # Run tests
    test_results = run_comprehensive_test()
    
    # Generate report
    report = generate_test_report(test_results)
    
    # Save report
    with open('test_report.md', 'w', encoding='utf-8') as f:
        f.write(report)
    
    # Display summary
    logger.info("\n" + "=" * 60)
    logger.info("📋 TEST SUMMARY")
    logger.info("=" * 60)
    
    status_emoji = {
        'success': '🎉',
        'partial': '⚠️',
        'failed': '❌'
    }
    
    overall_status = test_results['overall_status']
    logger.info(f"{status_emoji.get(overall_status, '❓')} Overall Status: {overall_status.upper()}")
    
    if overall_status == 'success':
        logger.info("✅ All tests passed! Pipeline is working correctly.")
    elif overall_status == 'partial':
        logger.info("⚠️ Pipeline files created but model deployment may have issues.")
    else:
        logger.info("❌ Pipeline has significant issues.")
    
    logger.info(f"📄 Detailed report saved to: test_report.md")
    
    return overall_status == 'success'

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
