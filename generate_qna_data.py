#!/usr/bin/env python3
"""
Script tự động generate câu hỏi-đáp án từ data AMS
Sử dụng API Ollama với model qwen3:30b-a3b
"""
import os
import json
import requests
import logging
from pathlib import Path
from typing import List, Dict

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Ollama API configuration
OLLAMA_API_URL = "http://**********:11434"
MODEL_NAME = "qwen3:30b-a3b"

def check_ollama_connection():
    """Kiểm tra kết nối đến Ollama API"""
    try:
        response = requests.get(f"{OLLAMA_API_URL}/api/tags", timeout=10)
        if response.status_code == 200:
            models = response.json().get('models', [])
            model_names = [model['name'] for model in models]
            logger.info(f"✅ Kết nối Ollama thành công. Models có sẵn: {model_names}")

            if MODEL_NAME in model_names:
                logger.info(f"✅ Model {MODEL_NAME} đã sẵn sàng")
                return True
            else:
                logger.warning(f"⚠️ Model {MODEL_NAME} không tìm thấy. Sẽ thử pull model...")
                return pull_model()
        else:
            logger.error(f"❌ Không thể kết nối Ollama: {response.status_code}")
            return False
    except Exception as e:
        logger.error(f"❌ Lỗi kết nối Ollama: {e}")
        return False

def pull_model():
    """Pull model nếu chưa có"""
    try:
        logger.info(f"📥 Đang pull model {MODEL_NAME}...")
        response = requests.post(
            f"{OLLAMA_API_URL}/api/pull",
            json={"name": MODEL_NAME},
            timeout=300
        )
        if response.status_code == 200:
            logger.info(f"✅ Pull model {MODEL_NAME} thành công")
            return True
        else:
            logger.error(f"❌ Không thể pull model: {response.status_code}")
            return False
    except Exception as e:
        logger.error(f"❌ Lỗi pull model: {e}")
        return False

def generate_comprehensive_qna(text: str, filename: str) -> List[Dict]:
    """Generate comprehensive QnA với nhiều chiến lược khác nhau"""
    all_qna = []

    # Strategy 1: Detailed Q&A
    detailed_qna = generate_detailed_qna(text, filename)
    all_qna.extend(detailed_qna)

    # Strategy 2: Scenario-based Q&A
    scenario_qna = generate_scenario_qna(text, filename)
    all_qna.extend(scenario_qna)

    # Strategy 3: Procedural Q&A
    procedural_qna = generate_procedural_qna(text, filename)
    all_qna.extend(procedural_qna)

    return all_qna

def generate_detailed_qna(text: str, filename: str) -> List[Dict]:
    """Generate detailed Q&A với thông tin chi tiết"""

    prompt = f"""Bạn là chuyên gia phân tích tài liệu và tạo câu hỏi-trả lời chi tiết. Hãy phân tích kỹ tài liệu "{filename}" và tạo 8-12 cặp câu hỏi-trả lời CHẤT LƯỢNG CAO.

TÀI LIỆU PHÂN TÍCH:
{text}

YÊU CẦU CHI TIẾT:
1. MỖI CÂU TRẢ LỜI PHẢI DÀI TỐI THIỂU 2-3 CÂU, bao gồm:
   - Thông tin chính
   - Chi tiết bổ sung
   - Ví dụ cụ thể (nếu có)
   - Lưu ý quan trọng

2. CÂU HỎI PHẢI ĐA DẠNG:
   - Câu hỏi về thông tin cơ bản (Gì, Ở đâu, Khi nào)
   - Câu hỏi về quy trình (Làm thế nào, Các bước)
   - Câu hỏi về điều kiện (Yêu cầu gì, Cần chuẩn bị gì)
   - Câu hỏi so sánh (Khác biệt, Ưu nhược điểm)

3. ĐỊNH DẠNG CHÍNH XÁC:
Câu hỏi: [câu hỏi chi tiết và cụ thể]
Trả lời: [trả lời đầy đủ 2-3 câu với thông tin chi tiết]

VÍ DỤ MẪU:
Câu hỏi: Quy định về thời gian làm việc tại công ty AMS như thế nào và có những lưu ý gì đặc biệt?
Trả lời: Thời gian làm việc tại AMS là từ 8h30 đến 17h30 các ngày trong tuần, với thời gian nghỉ trưa từ 12h đến 13h. Nhân viên cần đảm bảo đúng giờ và thông báo trước nếu có việc đột xuất. Công ty áp dụng chế độ chấm công nghiêm ngặt và có chính sách linh hoạt cho một số vị trí đặc biệt.

HÃY TẠO CÁC CẶP CÂU HỎI-TRẢ LỜI CHI TIẾT:"""

    return call_ollama_api(prompt, filename, "detailed")

def generate_scenario_qna(text: str, filename: str) -> List[Dict]:
    """Generate scenario-based Q&A với tình huống thực tế"""

    prompt = f"""Bạn là chuyên gia tư vấn nhân sự. Dựa trên tài liệu "{filename}", hãy tạo 6-10 cặp câu hỏi-trả lời dạng TÌNH HUỐNG THỰC TẾ.

TÀI LIỆU THAM KHẢO:
{text}

YÊU CẦU TÌNH HUỐNG:
1. Tạo câu hỏi dạng tình huống thực tế mà nhân viên thường gặp
2. Trả lời phải hướng dẫn cụ thể, từng bước
3. Bao gồm cả tình huống bình thường và đặc biệt
4. Đề cập đến quy trình, thủ tục cần thiết

ĐỊNH DẠNG:
Câu hỏi: [tình huống cụ thể với ngữ cảnh]
Trả lời: [hướng dẫn chi tiết từng bước]

VÍ DỤ TÌNH HUỐNG:
Câu hỏi: Tôi là nhân viên mới vừa gia nhập AMS, cần làm những thủ tục gì trong tuần đầu tiên và ai sẽ hướng dẫn tôi?
Trả lời: Trong tuần đầu tiên, bạn cần hoàn thành các thủ tục sau: (1) Ký hợp đồng lao động và các giấy tờ liên quan với phòng Nhân sự, (2) Tham gia chương trình định hướng cho nhân viên mới, (3) Thiết lập tài khoản email và các công cụ làm việc với IT, (4) Gặp gỡ quản lý trực tiếp để hiểu rõ công việc. Phòng Nhân sự sẽ là đầu mối chính hướng dẫn bạn trong suốt quá trình này.

TẠO CÁC TÌNH HUỐNG THỰC TẾ:"""

    return call_ollama_api(prompt, filename, "scenario")

def generate_procedural_qna(text: str, filename: str) -> List[Dict]:
    """Generate procedural Q&A về quy trình, thủ tục"""

    prompt = f"""Bạn là chuyên gia quy trình. Từ tài liệu "{filename}", hãy tạo 6-8 cặp câu hỏi-trả lời về QUY TRÌNH và THỦ TỤC.

TÀI LIỆU QUY TRÌNH:
{text}

YÊU CẦU QUY TRÌNH:
1. Tập trung vào các quy trình, thủ tục, bước thực hiện
2. Trả lời phải có cấu trúc rõ ràng: Bước 1, Bước 2, Bước 3...
3. Đề cập đến giấy tờ, tài liệu cần thiết
4. Nêu rõ thời gian, deadline nếu có
5. Chỉ ra người phụ trách, bộ phận liên quan

ĐỊNH DẠNG QUY TRÌNH:
Câu hỏi: [về quy trình, thủ tục cụ thể]
Trả lời: [các bước thực hiện có cấu trúc]

VÍ DỤ QUY TRÌNH:
Câu hỏi: Quy trình xin nghỉ phép tại AMS gồm những bước nào và cần chuẩn bị gì?
Trả lời: Quy trình xin nghỉ phép gồm các bước sau: Bước 1 - Điền đơn xin nghỉ phép ít nhất 1 ngày trước khi nghỉ. Bước 2 - Gửi đơn cho quản lý trực tiếp để phê duyệt. Bước 3 - Sau khi được phê duyệt, gửi bản sao cho phòng Nhân sự để cập nhật. Bước 4 - Bàn giao công việc cho đồng nghiệp nếu nghỉ dài ngày. Lưu ý: Đối với nghỉ phép đột xuất do bệnh, cần có giấy tờ y tế chứng minh.

TẠO CÁC QUY TRÌNH THỦ TỤC:"""

    return call_ollama_api(prompt, filename, "procedural")

def call_ollama_api(prompt: str, filename: str, qna_type: str) -> List[Dict]:
    """Gọi Ollama API với prompt đã tối ưu"""

    try:
        response = requests.post(
            f"{OLLAMA_API_URL}/api/generate",
            json={
                "model": MODEL_NAME,
                "prompt": prompt,
                "stream": False,
                "options": {
                    "temperature": 0.2,  # Lower for more consistent format
                    "top_p": 0.8,
                    "num_predict": 3000,  # More tokens for detailed answers
                    "repeat_penalty": 1.1
                }
            },
            timeout=180  # Longer timeout for detailed generation
        )

        if response.status_code == 200:
            result = response.json()
            generated_text = result.get('response', '')

            # Enhanced parsing for better QnA extraction
            qna_pairs = parse_enhanced_qna(generated_text, filename, qna_type)

            logger.info(f"✅ Generated {len(qna_pairs)} {qna_type} QnA pairs từ {filename}")
            return qna_pairs

        else:
            logger.error(f"❌ API call failed: {response.status_code}")
            return []

    except Exception as e:
        logger.error(f"❌ Lỗi generate {qna_type} QnA từ {filename}: {e}")
        return []

def parse_enhanced_qna(text: str, filename: str, qna_type: str) -> List[Dict]:
    """Enhanced parsing cho QnA với validation tốt hơn"""
    qna_pairs = []

    # Split by "Câu hỏi:" to get individual Q&A blocks
    blocks = text.split('Câu hỏi:')

    for i, block in enumerate(blocks):
        if i == 0:  # Skip first empty block
            continue

        block = block.strip()
        if not block:
            continue

        # Find "Trả lời:" in the block
        if 'Trả lời:' in block:
            parts = block.split('Trả lời:', 1)
            if len(parts) == 2:
                question = parts[0].strip()
                answer = parts[1].strip()

                # Clean up question and answer
                question = clean_text(question)
                answer = clean_text(answer)

                # Validate quality
                if validate_qna_quality(question, answer):
                    qna_pairs.append({
                        'question': question,
                        'answer': answer,
                        'source_file': filename,
                        'qna_type': qna_type,
                        'formatted': f"Câu hỏi: {question} Trả lời: {answer}",
                        'quality_score': calculate_quality_score(question, answer)
                    })

    return qna_pairs

def clean_text(text: str) -> str:
    """Clean và format text"""
    # Remove extra whitespace and newlines
    text = ' '.join(text.split())

    # Remove unwanted characters
    text = text.replace('**', '').replace('*', '')

    # Remove numbering if present
    import re
    text = re.sub(r'^\d+\.\s*', '', text)
    text = re.sub(r'^[a-zA-Z]\)\s*', '', text)

    return text.strip()

def validate_qna_quality(question: str, answer: str) -> bool:
    """Validate chất lượng QnA"""
    # Minimum length requirements
    if len(question) < 15 or len(answer) < 30:
        return False

    # Check for question words
    question_words = ['gì', 'ai', 'đâu', 'khi nào', 'như thế nào', 'tại sao', 'bao nhiêu', 'có', 'được', 'phải']
    if not any(word in question.lower() for word in question_words):
        return False

    # Check answer completeness (should have multiple sentences for detailed answers)
    answer_sentences = answer.count('.') + answer.count('!') + answer.count('?')
    if answer_sentences < 1:
        return False

    return True

def calculate_quality_score(question: str, answer: str) -> float:
    """Tính điểm chất lượng QnA"""
    score = 0.0

    # Length score
    if len(answer) > 100:
        score += 0.3
    elif len(answer) > 50:
        score += 0.2
    else:
        score += 0.1

    # Detail score (multiple sentences)
    sentences = answer.count('.') + answer.count('!') + answer.count('?')
    if sentences >= 3:
        score += 0.3
    elif sentences >= 2:
        score += 0.2
    else:
        score += 0.1

    # Keyword relevance
    keywords = ['ams', 'công ty', 'nhân viên', 'quy định', 'thời gian', 'địa chỉ', 'chính sách']
    keyword_count = sum(1 for keyword in keywords if keyword in answer.lower())
    score += min(keyword_count * 0.1, 0.4)

    return min(score, 1.0)

def generate_qna_from_text(text: str, filename: str) -> List[Dict]:
    """Main function - generate comprehensive QnA"""
    logger.info(f"🔄 Generating comprehensive QnA for {filename}...")

    # Use the new comprehensive approach
    all_qna = generate_comprehensive_qna(text, filename)

    # Sort by quality score
    all_qna.sort(key=lambda x: x.get('quality_score', 0), reverse=True)

    # Log quality statistics
    if all_qna:
        avg_score = sum(item.get('quality_score', 0) for item in all_qna) / len(all_qna)
        high_quality = sum(1 for item in all_qna if item.get('quality_score', 0) > 0.7)
        logger.info(f"📊 Quality stats: Avg={avg_score:.2f}, High quality={high_quality}/{len(all_qna)}")

    return all_qna

def read_data_files(data_dir: str = "./data") -> Dict[str, str]:
    """Đọc tất cả file .txt trong thư mục data"""
    data_path = Path(data_dir)
    if not data_path.exists():
        logger.error(f"❌ Thư mục {data_dir} không tồn tại!")
        return {}

    files_content = {}
    for txt_file in data_path.glob("*.txt"):
        try:
            with open(txt_file, 'r', encoding='utf-8') as f:
                content = f.read().strip()
                if content:
                    files_content[txt_file.name] = content
                    logger.info(f"📖 Đọc file {txt_file.name}: {len(content)} ký tự")
        except Exception as e:
            logger.error(f"❌ Lỗi đọc file {txt_file}: {e}")

    return files_content

def save_qna_data(qna_data: List[Dict], output_file: str = "generated_qna_data.json"):
    """Lưu dữ liệu QnA vào file JSON"""
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(qna_data, f, ensure_ascii=False, indent=2)
        logger.info(f"✅ Đã lưu {len(qna_data)} QnA pairs vào {output_file}")
        return True
    except Exception as e:
        logger.error(f"❌ Lỗi lưu file {output_file}: {e}")
        return False

def create_training_format(qna_data: List[Dict], output_file: str = "training_data.json"):
    """Tạo format dữ liệu cho training"""
    training_data = []

    for item in qna_data:
        # Format cho training
        training_data.append(item['formatted'])

    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(training_data, f, ensure_ascii=False, indent=2)
        logger.info(f"✅ Đã tạo training data với {len(training_data)} samples vào {output_file}")
        return True
    except Exception as e:
        logger.error(f"❌ Lỗi tạo training data: {e}")
        return False

def check_existing_qna_data(files_content: Dict[str, str]) -> tuple:
    """Kiểm tra QnA data đã có và files nào cần generate mới"""
    existing_data = []
    files_to_process = {}

    # Kiểm tra file QnA đã có
    if os.path.exists("generated_qna_data.json"):
        try:
            with open("generated_qna_data.json", 'r', encoding='utf-8') as f:
                existing_data = json.load(f)
            logger.info(f"📦 Tìm thấy {len(existing_data)} QnA pairs đã có")
        except Exception as e:
            logger.warning(f"⚠️ Lỗi đọc QnA data cũ: {e}")
            existing_data = []

    # Tạo hash của files đã xử lý
    processed_files = set()
    for item in existing_data:
        source_file = item.get('source_file', '')
        if source_file:
            processed_files.add(source_file.split('_chunk_')[0])  # Remove chunk suffix

    # Xác định files cần xử lý
    for filename, content in files_content.items():
        if filename not in processed_files:
            files_to_process[filename] = content
            logger.info(f"🆕 File mới cần xử lý: {filename}")
        else:
            logger.info(f"✅ File đã xử lý: {filename}")

    return existing_data, files_to_process

def optimize_qna_quality(qna_data: List[Dict]) -> List[Dict]:
    """Tối ưu chất lượng QnA data"""
    logger.info("🔍 Tối ưu chất lượng QnA data...")

    optimized_data = []
    seen_questions = set()

    for item in qna_data:
        question = item.get('question', '').strip().lower()
        answer = item.get('answer', '').strip()

        # Loại bỏ câu hỏi trùng lặp
        if question in seen_questions:
            continue

        # Kiểm tra chất lượng câu hỏi
        if len(question) < 10 or len(answer) < 20:
            continue

        # Kiểm tra có chứa từ khóa quan trọng
        important_keywords = ['ams', 'công ty', 'nhân viên', 'quy định', 'chính sách', 'thời gian', 'địa chỉ']
        if not any(keyword in question for keyword in important_keywords):
            continue

        seen_questions.add(question)
        optimized_data.append(item)

    logger.info(f"✅ Tối ưu từ {len(qna_data)} xuống {len(optimized_data)} QnA pairs chất lượng cao")
    return optimized_data

def main():
    """Main function với caching và tối ưu"""
    logger.info("🚀 Bắt đầu generate QnA data từ AMS documents (với caching)")

    # Kiểm tra kết nối Ollama
    if not check_ollama_connection():
        logger.error("❌ Không thể kết nối đến Ollama API")
        return False

    # Đọc tất cả file data
    files_content = read_data_files()
    if not files_content:
        logger.error("❌ Không có file data nào để xử lý")
        return False

    # Kiểm tra QnA data đã có
    existing_data, files_to_process = check_existing_qna_data(files_content)

    if not files_to_process:
        logger.info("✅ Tất cả files đã được xử lý, sử dụng QnA data có sẵn")
        all_qna_data = existing_data
    else:
        logger.info(f"🔄 Cần xử lý {len(files_to_process)} files mới")

        # Generate QnA cho files mới
        new_qna_data = []
        for filename, content in files_to_process.items():
            logger.info(f"🔄 Đang xử lý file: {filename}")

            # Chia nhỏ content nếu quá dài (max 3500 chars mỗi chunk để tối ưu)
            max_chunk_size = 3500
            if len(content) > max_chunk_size:
                chunks = [content[i:i+max_chunk_size] for i in range(0, len(content), max_chunk_size)]
                logger.info(f"📄 Chia file {filename} thành {len(chunks)} chunks")

                for i, chunk in enumerate(chunks):
                    chunk_filename = f"{filename}_chunk_{i+1}"
                    qna_pairs = generate_qna_from_text(chunk, chunk_filename)
                    new_qna_data.extend(qna_pairs)
            else:
                qna_pairs = generate_qna_from_text(content, filename)
                new_qna_data.extend(qna_pairs)

        # Kết hợp data cũ và mới
        all_qna_data = existing_data + new_qna_data
        logger.info(f"📊 Tổng cộng: {len(existing_data)} cũ + {len(new_qna_data)} mới = {len(all_qna_data)} QnA pairs")

    if not all_qna_data:
        logger.error("❌ Không có QnA data nào")
        return False

    # Tối ưu chất lượng
    optimized_qna_data = optimize_qna_quality(all_qna_data)

    # Lưu dữ liệu
    logger.info(f"💾 Lưu {len(optimized_qna_data)} QnA pairs chất lượng cao")

    # Lưu dữ liệu chi tiết
    if not save_qna_data(optimized_qna_data, "generated_qna_data.json"):
        return False

    # Tạo format cho training
    if not create_training_format(optimized_qna_data, "training_data.json"):
        return False

    # Thống kê chi tiết
    logger.info("📊 Thống kê chi tiết:")
    source_stats = {}
    for item in optimized_qna_data:
        source = item['source_file'].split('_chunk_')[0]  # Remove chunk suffix
        source_stats[source] = source_stats.get(source, 0) + 1

    for source, count in source_stats.items():
        logger.info(f"  - {source}: {count} QnA pairs")

    # Thống kê chất lượng
    avg_question_length = sum(len(item['question']) for item in optimized_qna_data) / len(optimized_qna_data)
    avg_answer_length = sum(len(item['answer']) for item in optimized_qna_data) / len(optimized_qna_data)

    logger.info("📈 Chất lượng QnA:")
    logger.info(f"  - Độ dài câu hỏi trung bình: {avg_question_length:.1f} ký tự")
    logger.info(f"  - Độ dài câu trả lời trung bình: {avg_answer_length:.1f} ký tự")

    logger.info("🎉 Hoàn thành generate QnA data!")
    logger.info("📁 Files được tạo:")
    logger.info("  - generated_qna_data.json: Dữ liệu chi tiết (đã tối ưu)")
    logger.info("  - training_data.json: Dữ liệu cho training")

    return True

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
