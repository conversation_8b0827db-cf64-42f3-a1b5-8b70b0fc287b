#!/usr/bin/env python3
"""
Script tự động generate câu hỏi-đáp án từ data AMS
Sử dụng API Ollama với model qwen3:30b-a3b
"""
import os
import json
import requests
import logging
from pathlib import Path
from typing import List, Dict

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Ollama API configuration
OLLAMA_API_URL = "http://**********:11434"
MODEL_NAME = "qwen3:30b-a3b"

def check_ollama_connection():
    """Kiểm tra kết nối đến Ollama API"""
    try:
        response = requests.get(f"{OLLAMA_API_URL}/api/tags", timeout=10)
        if response.status_code == 200:
            models = response.json().get('models', [])
            model_names = [model['name'] for model in models]
            logger.info(f"✅ Kết nối Ollama thành công. Models có sẵn: {model_names}")

            if MODEL_NAME in model_names:
                logger.info(f"✅ Model {MODEL_NAME} đã sẵn sàng")
                return True
            else:
                logger.warning(f"⚠️ Model {MODEL_NAME} không tìm thấy. Sẽ thử pull model...")
                return pull_model()
        else:
            logger.error(f"❌ Không thể kết nối Ollama: {response.status_code}")
            return False
    except Exception as e:
        logger.error(f"❌ Lỗi kết nối Ollama: {e}")
        return False

def pull_model():
    """Pull model nếu chưa có"""
    try:
        logger.info(f"📥 Đang pull model {MODEL_NAME}...")
        response = requests.post(
            f"{OLLAMA_API_URL}/api/pull",
            json={"name": MODEL_NAME},
            timeout=300
        )
        if response.status_code == 200:
            logger.info(f"✅ Pull model {MODEL_NAME} thành công")
            return True
        else:
            logger.error(f"❌ Không thể pull model: {response.status_code}")
            return False
    except Exception as e:
        logger.error(f"❌ Lỗi pull model: {e}")
        return False

def generate_qna_from_text(text: str, filename: str) -> List[Dict]:
    """Generate câu hỏi-đáp án từ text sử dụng Ollama API với prompt tối ưu"""

    prompt = f"""Bạn là chuyên gia tạo bộ câu hỏi-trả lời chất lượng cao. Dựa trên tài liệu "{filename}", hãy tạo 12-18 cặp câu hỏi-trả lời bằng tiếng Việt.

NỘI DUNG TÀI LIỆU:
{text}

YÊU CẦU CHẤT LƯỢNG:
1. Câu hỏi đa dạng: thông tin cơ bản, quy định cụ thể, thủ tục, chính sách
2. Câu hỏi phải tự nhiên như người dùng thực tế sẽ hỏi
3. Trả lời chính xác, đầy đủ, dễ hiểu
4. Bao gồm cả câu hỏi ngắn và câu hỏi phức tạp
5. Sử dụng từ khóa quan trọng từ tài liệu

ĐỊNH DẠNG BẮT BUỘC:
Câu hỏi: [câu hỏi cụ thể] Trả lời: [trả lời chi tiết]

VÍ DỤ CHẤT LƯỢNG CAO:
Câu hỏi: Địa chỉ trụ sở chính của công ty AMS ở đâu? Trả lời: Công ty AMS có trụ sở chính tại Nhà số 10, ngõ 7, phố Nguyễn Khả Trạc, phường Mai Dịch, quận Cầu Giấy, Hà Nội.
Câu hỏi: Nhân viên mới cần biết những quy định gì về thời gian thử việc? Trả lời: Thời gian thử việc là 60 ngày đối với nhân viên thường và có thể kéo dài đến 90 ngày đối với cấp quản lý. Thu nhập thử việc tối thiểu 85% lương chính thức.

TẠO CÁC CẶP CÂU HỎI-TRẢ LỜI:"""

    try:
        response = requests.post(
            f"{OLLAMA_API_URL}/api/generate",
            json={
                "model": MODEL_NAME,
                "prompt": prompt,
                "stream": False,
                "options": {
                    "temperature": 0.3,
                    "top_p": 0.9,
                    "num_predict": 2000
                }
            },
            timeout=120
        )

        if response.status_code == 200:
            result = response.json()
            generated_text = result.get('response', '')

            # Parse câu hỏi-đáp án từ response
            qna_pairs = []
            lines = generated_text.strip().split('\n')

            for line in lines:
                line = line.strip()
                if line and 'Câu hỏi:' in line and 'Trả lời:' in line:
                    # Tách câu hỏi và trả lời
                    parts = line.split('Trả lời:', 1)
                    if len(parts) == 2:
                        question = parts[0].replace('Câu hỏi:', '').strip()
                        answer = parts[1].strip()

                        if question and answer:
                            qna_pairs.append({
                                'question': question,
                                'answer': answer,
                                'source_file': filename,
                                'formatted': f"Câu hỏi: {question} Trả lời: {answer}"
                            })

            logger.info(f"✅ Generated {len(qna_pairs)} QnA pairs từ {filename}")
            return qna_pairs

        else:
            logger.error(f"❌ API call failed: {response.status_code}")
            return []

    except Exception as e:
        logger.error(f"❌ Lỗi generate QnA từ {filename}: {e}")
        return []

def read_data_files(data_dir: str = "./data") -> Dict[str, str]:
    """Đọc tất cả file .txt trong thư mục data"""
    data_path = Path(data_dir)
    if not data_path.exists():
        logger.error(f"❌ Thư mục {data_dir} không tồn tại!")
        return {}

    files_content = {}
    for txt_file in data_path.glob("*.txt"):
        try:
            with open(txt_file, 'r', encoding='utf-8') as f:
                content = f.read().strip()
                if content:
                    files_content[txt_file.name] = content
                    logger.info(f"📖 Đọc file {txt_file.name}: {len(content)} ký tự")
        except Exception as e:
            logger.error(f"❌ Lỗi đọc file {txt_file}: {e}")

    return files_content

def save_qna_data(qna_data: List[Dict], output_file: str = "generated_qna_data.json"):
    """Lưu dữ liệu QnA vào file JSON"""
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(qna_data, f, ensure_ascii=False, indent=2)
        logger.info(f"✅ Đã lưu {len(qna_data)} QnA pairs vào {output_file}")
        return True
    except Exception as e:
        logger.error(f"❌ Lỗi lưu file {output_file}: {e}")
        return False

def create_training_format(qna_data: List[Dict], output_file: str = "training_data.json"):
    """Tạo format dữ liệu cho training"""
    training_data = []

    for item in qna_data:
        # Format cho training
        training_data.append(item['formatted'])

    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(training_data, f, ensure_ascii=False, indent=2)
        logger.info(f"✅ Đã tạo training data với {len(training_data)} samples vào {output_file}")
        return True
    except Exception as e:
        logger.error(f"❌ Lỗi tạo training data: {e}")
        return False

def check_existing_qna_data(files_content: Dict[str, str]) -> tuple:
    """Kiểm tra QnA data đã có và files nào cần generate mới"""
    existing_data = []
    files_to_process = {}

    # Kiểm tra file QnA đã có
    if os.path.exists("generated_qna_data.json"):
        try:
            with open("generated_qna_data.json", 'r', encoding='utf-8') as f:
                existing_data = json.load(f)
            logger.info(f"📦 Tìm thấy {len(existing_data)} QnA pairs đã có")
        except Exception as e:
            logger.warning(f"⚠️ Lỗi đọc QnA data cũ: {e}")
            existing_data = []

    # Tạo hash của files đã xử lý
    processed_files = set()
    for item in existing_data:
        source_file = item.get('source_file', '')
        if source_file:
            processed_files.add(source_file.split('_chunk_')[0])  # Remove chunk suffix

    # Xác định files cần xử lý
    for filename, content in files_content.items():
        if filename not in processed_files:
            files_to_process[filename] = content
            logger.info(f"🆕 File mới cần xử lý: {filename}")
        else:
            logger.info(f"✅ File đã xử lý: {filename}")

    return existing_data, files_to_process

def optimize_qna_quality(qna_data: List[Dict]) -> List[Dict]:
    """Tối ưu chất lượng QnA data"""
    logger.info("🔍 Tối ưu chất lượng QnA data...")

    optimized_data = []
    seen_questions = set()

    for item in qna_data:
        question = item.get('question', '').strip().lower()
        answer = item.get('answer', '').strip()

        # Loại bỏ câu hỏi trùng lặp
        if question in seen_questions:
            continue

        # Kiểm tra chất lượng câu hỏi
        if len(question) < 10 or len(answer) < 20:
            continue

        # Kiểm tra có chứa từ khóa quan trọng
        important_keywords = ['ams', 'công ty', 'nhân viên', 'quy định', 'chính sách', 'thời gian', 'địa chỉ']
        if not any(keyword in question for keyword in important_keywords):
            continue

        seen_questions.add(question)
        optimized_data.append(item)

    logger.info(f"✅ Tối ưu từ {len(qna_data)} xuống {len(optimized_data)} QnA pairs chất lượng cao")
    return optimized_data

def main():
    """Main function với caching và tối ưu"""
    logger.info("🚀 Bắt đầu generate QnA data từ AMS documents (với caching)")

    # Kiểm tra kết nối Ollama
    if not check_ollama_connection():
        logger.error("❌ Không thể kết nối đến Ollama API")
        return False

    # Đọc tất cả file data
    files_content = read_data_files()
    if not files_content:
        logger.error("❌ Không có file data nào để xử lý")
        return False

    # Kiểm tra QnA data đã có
    existing_data, files_to_process = check_existing_qna_data(files_content)

    if not files_to_process:
        logger.info("✅ Tất cả files đã được xử lý, sử dụng QnA data có sẵn")
        all_qna_data = existing_data
    else:
        logger.info(f"🔄 Cần xử lý {len(files_to_process)} files mới")

        # Generate QnA cho files mới
        new_qna_data = []
        for filename, content in files_to_process.items():
            logger.info(f"🔄 Đang xử lý file: {filename}")

            # Chia nhỏ content nếu quá dài (max 3500 chars mỗi chunk để tối ưu)
            max_chunk_size = 3500
            if len(content) > max_chunk_size:
                chunks = [content[i:i+max_chunk_size] for i in range(0, len(content), max_chunk_size)]
                logger.info(f"📄 Chia file {filename} thành {len(chunks)} chunks")

                for i, chunk in enumerate(chunks):
                    chunk_filename = f"{filename}_chunk_{i+1}"
                    qna_pairs = generate_qna_from_text(chunk, chunk_filename)
                    new_qna_data.extend(qna_pairs)
            else:
                qna_pairs = generate_qna_from_text(content, filename)
                new_qna_data.extend(qna_pairs)

        # Kết hợp data cũ và mới
        all_qna_data = existing_data + new_qna_data
        logger.info(f"📊 Tổng cộng: {len(existing_data)} cũ + {len(new_qna_data)} mới = {len(all_qna_data)} QnA pairs")

    if not all_qna_data:
        logger.error("❌ Không có QnA data nào")
        return False

    # Tối ưu chất lượng
    optimized_qna_data = optimize_qna_quality(all_qna_data)

    # Lưu dữ liệu
    logger.info(f"💾 Lưu {len(optimized_qna_data)} QnA pairs chất lượng cao")

    # Lưu dữ liệu chi tiết
    if not save_qna_data(optimized_qna_data, "generated_qna_data.json"):
        return False

    # Tạo format cho training
    if not create_training_format(optimized_qna_data, "training_data.json"):
        return False

    # Thống kê chi tiết
    logger.info("📊 Thống kê chi tiết:")
    source_stats = {}
    for item in optimized_qna_data:
        source = item['source_file'].split('_chunk_')[0]  # Remove chunk suffix
        source_stats[source] = source_stats.get(source, 0) + 1

    for source, count in source_stats.items():
        logger.info(f"  - {source}: {count} QnA pairs")

    # Thống kê chất lượng
    avg_question_length = sum(len(item['question']) for item in optimized_qna_data) / len(optimized_qna_data)
    avg_answer_length = sum(len(item['answer']) for item in optimized_qna_data) / len(optimized_qna_data)

    logger.info("📈 Chất lượng QnA:")
    logger.info(f"  - Độ dài câu hỏi trung bình: {avg_question_length:.1f} ký tự")
    logger.info(f"  - Độ dài câu trả lời trung bình: {avg_answer_length:.1f} ký tự")

    logger.info("🎉 Hoàn thành generate QnA data!")
    logger.info("📁 Files được tạo:")
    logger.info("  - generated_qna_data.json: Dữ liệu chi tiết (đã tối ưu)")
    logger.info("  - training_data.json: Dữ liệu cho training")

    return True

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
