#!/usr/bin/env python3
"""
Script tự động generate câu hỏi-đáp án từ data AMS
Sử dụng API Ollama với model qwen3:30b-a3b
"""
import os
import json
import requests
import logging
from pathlib import Path
from typing import List, Dict

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Ollama API configuration
OLLAMA_API_URL = "http://**********:11434"
MODEL_NAME = "qwen3:30b-a3b"

def check_ollama_connection():
    """Kiểm tra kết nối đến Ollama API"""
    try:
        response = requests.get(f"{OLLAMA_API_URL}/api/tags", timeout=10)
        if response.status_code == 200:
            models = response.json().get('models', [])
            model_names = [model['name'] for model in models]
            logger.info(f"✅ Kết nối Ollama thành công. Models có sẵn: {model_names}")
            
            if MODEL_NAME in model_names:
                logger.info(f"✅ Model {MODEL_NAME} đã sẵn sàng")
                return True
            else:
                logger.warning(f"⚠️ Model {MODEL_NAME} không tìm thấy. Sẽ thử pull model...")
                return pull_model()
        else:
            logger.error(f"❌ Không thể kết nối Ollama: {response.status_code}")
            return False
    except Exception as e:
        logger.error(f"❌ Lỗi kết nối Ollama: {e}")
        return False

def pull_model():
    """Pull model nếu chưa có"""
    try:
        logger.info(f"📥 Đang pull model {MODEL_NAME}...")
        response = requests.post(
            f"{OLLAMA_API_URL}/api/pull",
            json={"name": MODEL_NAME},
            timeout=300
        )
        if response.status_code == 200:
            logger.info(f"✅ Pull model {MODEL_NAME} thành công")
            return True
        else:
            logger.error(f"❌ Không thể pull model: {response.status_code}")
            return False
    except Exception as e:
        logger.error(f"❌ Lỗi pull model: {e}")
        return False

def generate_qna_from_text(text: str, filename: str) -> List[Dict]:
    """Generate câu hỏi-đáp án từ text sử dụng Ollama API"""
    
    prompt = f"""Dựa trên nội dung tài liệu "{filename}" sau đây, hãy tạo ra 10-15 cặp câu hỏi và trả lời bằng tiếng Việt.

Nội dung tài liệu:
{text}

Yêu cầu:
1. Câu hỏi phải cụ thể và liên quan trực tiếp đến nội dung
2. Trả lời phải chính xác và đầy đủ thông tin
3. Định dạng: "Câu hỏi: [câu hỏi] Trả lời: [trả lời]"
4. Mỗi cặp câu hỏi-trả lời trên một dòng riêng
5. Không thêm số thứ tự hay ký tự đặc biệt

Ví dụ:
Câu hỏi: Công ty AMS có địa chỉ ở đâu? Trả lời: Công ty AMS có trụ sở chính tại Nhà số 10, ngõ 7, phố Nguyễn Khả Trạc, phường Mai Dịch, quận Cầu Giấy, Hà Nội.

Hãy tạo các cặp câu hỏi-trả lời:"""

    try:
        response = requests.post(
            f"{OLLAMA_API_URL}/api/generate",
            json={
                "model": MODEL_NAME,
                "prompt": prompt,
                "stream": False,
                "options": {
                    "temperature": 0.3,
                    "top_p": 0.9,
                    "num_predict": 2000
                }
            },
            timeout=120
        )
        
        if response.status_code == 200:
            result = response.json()
            generated_text = result.get('response', '')
            
            # Parse câu hỏi-đáp án từ response
            qna_pairs = []
            lines = generated_text.strip().split('\n')
            
            for line in lines:
                line = line.strip()
                if line and 'Câu hỏi:' in line and 'Trả lời:' in line:
                    # Tách câu hỏi và trả lời
                    parts = line.split('Trả lời:', 1)
                    if len(parts) == 2:
                        question = parts[0].replace('Câu hỏi:', '').strip()
                        answer = parts[1].strip()
                        
                        if question and answer:
                            qna_pairs.append({
                                'question': question,
                                'answer': answer,
                                'source_file': filename,
                                'formatted': f"Câu hỏi: {question} Trả lời: {answer}"
                            })
            
            logger.info(f"✅ Generated {len(qna_pairs)} QnA pairs từ {filename}")
            return qna_pairs
            
        else:
            logger.error(f"❌ API call failed: {response.status_code}")
            return []
            
    except Exception as e:
        logger.error(f"❌ Lỗi generate QnA từ {filename}: {e}")
        return []

def read_data_files(data_dir: str = "./data") -> Dict[str, str]:
    """Đọc tất cả file .txt trong thư mục data"""
    data_path = Path(data_dir)
    if not data_path.exists():
        logger.error(f"❌ Thư mục {data_dir} không tồn tại!")
        return {}
    
    files_content = {}
    for txt_file in data_path.glob("*.txt"):
        try:
            with open(txt_file, 'r', encoding='utf-8') as f:
                content = f.read().strip()
                if content:
                    files_content[txt_file.name] = content
                    logger.info(f"📖 Đọc file {txt_file.name}: {len(content)} ký tự")
        except Exception as e:
            logger.error(f"❌ Lỗi đọc file {txt_file}: {e}")
    
    return files_content

def save_qna_data(qna_data: List[Dict], output_file: str = "generated_qna_data.json"):
    """Lưu dữ liệu QnA vào file JSON"""
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(qna_data, f, ensure_ascii=False, indent=2)
        logger.info(f"✅ Đã lưu {len(qna_data)} QnA pairs vào {output_file}")
        return True
    except Exception as e:
        logger.error(f"❌ Lỗi lưu file {output_file}: {e}")
        return False

def create_training_format(qna_data: List[Dict], output_file: str = "training_data.json"):
    """Tạo format dữ liệu cho training"""
    training_data = []
    
    for item in qna_data:
        # Format cho training
        training_data.append(item['formatted'])
    
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(training_data, f, ensure_ascii=False, indent=2)
        logger.info(f"✅ Đã tạo training data với {len(training_data)} samples vào {output_file}")
        return True
    except Exception as e:
        logger.error(f"❌ Lỗi tạo training data: {e}")
        return False

def main():
    """Main function"""
    logger.info("🚀 Bắt đầu generate QnA data từ AMS documents")
    
    # Kiểm tra kết nối Ollama
    if not check_ollama_connection():
        logger.error("❌ Không thể kết nối đến Ollama API")
        return False
    
    # Đọc tất cả file data
    files_content = read_data_files()
    if not files_content:
        logger.error("❌ Không có file data nào để xử lý")
        return False
    
    # Generate QnA cho từng file
    all_qna_data = []
    for filename, content in files_content.items():
        logger.info(f"🔄 Đang xử lý file: {filename}")
        
        # Chia nhỏ content nếu quá dài (max 4000 chars mỗi chunk)
        max_chunk_size = 4000
        if len(content) > max_chunk_size:
            chunks = [content[i:i+max_chunk_size] for i in range(0, len(content), max_chunk_size)]
            logger.info(f"📄 Chia file {filename} thành {len(chunks)} chunks")
            
            for i, chunk in enumerate(chunks):
                chunk_filename = f"{filename}_chunk_{i+1}"
                qna_pairs = generate_qna_from_text(chunk, chunk_filename)
                all_qna_data.extend(qna_pairs)
        else:
            qna_pairs = generate_qna_from_text(content, filename)
            all_qna_data.extend(qna_pairs)
    
    if not all_qna_data:
        logger.error("❌ Không generate được QnA data nào")
        return False
    
    # Lưu dữ liệu
    logger.info(f"💾 Tổng cộng generated {len(all_qna_data)} QnA pairs")
    
    # Lưu dữ liệu chi tiết
    if not save_qna_data(all_qna_data, "generated_qna_data.json"):
        return False
    
    # Tạo format cho training
    if not create_training_format(all_qna_data, "training_data.json"):
        return False
    
    # Thống kê
    logger.info("📊 Thống kê:")
    source_stats = {}
    for item in all_qna_data:
        source = item['source_file']
        source_stats[source] = source_stats.get(source, 0) + 1
    
    for source, count in source_stats.items():
        logger.info(f"  - {source}: {count} QnA pairs")
    
    logger.info("🎉 Hoàn thành generate QnA data!")
    logger.info("📁 Files được tạo:")
    logger.info("  - generated_qna_data.json: Dữ liệu chi tiết")
    logger.info("  - training_data.json: Dữ liệu cho training")
    
    return True

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
