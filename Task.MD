# Hyperparameter Optimization and QnA Generation for Qwen3:1.7B and Qwen3:4B on Tesla P40

## Objective
Guide an AI coding agent to optimize hyperparameters for fine-tuning Qwen3:1.7B and Qwen3:4B models (already available: qwen3:1.7b, 1.4 GB, ID 458ce03a2187; qwen3:4b, 2.6 GB, ID a383baf4993b) on a Tesla P40 GPU via SSH (ailab@**********), using Ollama API for QnA dataset generation, with a trial run for validation.

## Prerequisites
- **SSH Access**: Valid credentials for `ailab@**********`.
- **GPU**: Tesla P40 (24GB VRAM) available.
- **Ollama API**: Running on `http://localhost:11434`.
- **Models**: Qwen3:1.7B (ID: 458ce03a2187, 1.4 GB), Qwen3:4B (ID: a383baf4993b, 2.6 GB) pre-loaded.
- **Environment**: Python 3.8+, PyTorch (CUDA-enabled), Hugging Face Transformers, requests.
- **Data**: Text files (e.g., employee handbooks) in `/home/<USER>/data/`.

## Step-by-Step Implementation

### 1. SSH into the Server
- Connect:
  ```
  ssh ailab@**********
  ```
- Verify GPU and Ollama:
  ```
  nvidia-smi  # Check Tesla P40
  curl http://localhost:11434/api/tags  # Check Ollama API
  ```
- Expected: Tesla P40 listed, Ollama shows model list.

### 2. Set Up Environment
- Navigate to directory:
  ```
  cd /home/<USER>/projects/qwen3_finetune
  ```
- Create and activate virtual environment:
  ```
  python3 -m venv env
  source env/bin/activate
  ```
- Install packages:
  ```
  pip install torch==1.13.1+cu117 -f https://download.pytorch.org/whl/torch_stable.html
  pip install transformers==4.35.2 requests pandas numpy
  ```
- Verify GPU:
  ```
  python -c "import torch; print(torch.cuda.is_available()); print(torch.cuda.get_device_name(0))"
  ```
- Expected: `True`, `Tesla P40`.

### 3. Intelligent QnA Dataset Generation with Ollama
- Generate QnA pairs using Ollama API:
  ```
  import requests
  import pandas as pd
  from pathlib import Path

  # Read text files
  data_dir = Path("/home/<USER>/data")
  texts = []
  for file in data_dir.glob("*.txt"):
      with open(file, "r", encoding="utf-8") as f:
          texts.append({"file": file.name, "content": f.read()})

  # Function to query Ollama for QnA
  def generate_qna(text, model="llama3"):
      prompt = f"From this text, generate 5 relevant question-answer pairs:\n\n{text}"
      response = requests.post(
          "http://localhost:11434/api/generate",
          json={"model": model, "prompt": prompt, "stream": False}
      )
      if response.status_code == 200:
          result = response.json()["response"]
          return result
      else:
          raise Exception(f"Ollama API error: {response.text}")

  # Generate QnA pairs
  qna_data = []
  for doc in texts:
      try:
          qna_text = generate_qna(doc["content"])
          pairs = qna_text.split("\n\n")
          for pair in pairs:
              if "Question:" in pair and "Answer:" in pair:
                  q = pair.split("Answer:")[0].replace("Question:", "").strip()
                  a = pair.split("Answer:")[1].strip()
                  qna_data.append({"question": q, "answer": a, "source": doc["file"]})
      except Exception as e:
          print(f"Error processing {doc['file']}: {e}")

  # Save to CSV
  df = pd.DataFrame(qna_data)
  df.to_csv("/home/<USER>/projects/qwen3_finetune/qna_dataset.csv", index=False)
  print(f"Generated {len(qna_data)} QnA pairs, saved to qna_dataset.csv")
  ```

### 4. Load Model and Data
- Load models (Qwen3:1.7B or Qwen3:4B) and tokenizer:
  ```
  from transformers import AutoModelForCausalLM, AutoTokenizer
  model_name = "Qwen/Qwen3-1.7B"  # Switch to "Qwen/Qwen3-4B" for 4B
  model = AutoModelForCausalLM.from_pretrained(model_name, device_map="cuda:0")
  tokenizer = AutoTokenizer.from_pretrained(model_name)
  ```
- Note: Assumes models (IDs: 458ce03a2187, a383baf4993b) are pre-loaded and accessible via Hugging Face paths.
- Load and tokenize QnA dataset:
  ```
  import pandas as pd
  df = pd.read_csv("/home/<USER>/projects/qwen3_finetune/qna_dataset.csv")
  qna_pairs = df[["question", "answer"]].values.tolist()
  inputs = tokenizer(
      [f"Q: {q} A: {a}" for q, a in qna_pairs],
      return_tensors="pt", padding=True, truncation=True, max_length=512
  )
  inputs = {k: v.to("cuda:0") for k, v in inputs.items()}
  ```

### 5. Trial Run
- Test setup for Qwen3:1.7B (repeat for 4B if needed):
  ```
  from transformers import Trainer, TrainingArguments
  trial_args = TrainingArguments(
      output_dir="/home/<USER>/projects/qwen3_finetune/trial",
      learning_rate=2e-5,
      per_device_train_batch_size=8,
      gradient_accumulation_steps=2,
      warmup_steps=100,
      num_train_epochs=1,
      fp16=True,  # Mixed precision for Tesla P40
      logging_dir="/home/<USER>/projects/qwen3_finetune/logs",
      logging_steps=10,
      max_steps=50  # Short run for testing
  )
  trainer = Trainer(model=model, args=trial_args, train_dataset=inputs)
  trainer.train()
  torch.cuda.empty_cache()
  print("Trial run for Qwen3:1.7B complete. Check logs in /home/<USER>/projects/qwen3_finetune/logs")
  ```
- Test inference:
  ```
  sample_text = "Q: What’s the policy on smoking at AMS? A:"
  inputs = tokenizer(sample_text, return_tensors="pt").to("cuda:0")
  outputs = model.generate(**inputs, max_length=100)
  print(tokenizer.decode(outputs[0], skip_special_tokens=True))
  ```

### 6. Define Hyperparameter Search Space
- Hyperparameters for Tesla P40:
  - **Learning Rate**: [1e-5, 2e-5, 5e-5]
  - **Batch Size**: [4, 8] for 1.7B; [2, 4] for 4B (4B needs less VRAM usage)
  - **Gradient Accumulation Steps**: [1, 2, 4]
  - **Warmup Steps**: [0, 100, 200]
  - **Epochs**: [3, 5]
- Store ranges:
  ```
  hyperparams = {
      "learning_rate": [1e-5, 2e-5, 5e-5],
      "batch_size": [4, 8] if model_name.endswith("1.7B") else [2, 4],
      "gradient_accumulation_steps": [1, 2, 4],
      "warmup_steps": [0, 100, 200],
      "num_train_epochs": [3, 5]
  }
  ```

### 7. Implement Hyperparameter Optimization
- Grid search for optimization:
  ```
  from transformers import Trainer, TrainingArguments
  import itertools
  import torch

  def compute_metrics(eval_pred):
      predictions, labels = eval_pred
      loss = torch.nn.functional.cross_entropy(predictions, labels)
      return {"loss": loss.item()}

  best_loss = float("inf")
  best_params = None
  for lr, bs, gas, ws, epochs in itertools.product(
      hyperparams["learning_rate"],
      hyperparams["batch_size"],
      hyperparams["gradient_accumulation_steps"],
      hyperparams["warmup_steps"],
      hyperparams["num_train_epochs"]
  ):
      training_args = TrainingArguments(
          output_dir="/home/<USER>/projects/qwen3_finetune/checkpoints",
          learning_rate=lr,
          per_device_train_batch_size=bs,
          gradient_accumulation_steps=gas,
          warmup_steps=ws,
          num_train_epochs=epochs,
          fp16=True,
          logging_dir="/home/<USER>/projects/qwen3_finetune/logs",
          logging_steps=100,
          save_steps=500,
          evaluation_strategy="steps",
          eval_steps=500,
          load_best_model_at_end=True,
          report_to="none"
      )
      trainer = Trainer(
          model=model,
          args=training_args,
          train_dataset=inputs,
          eval_dataset=inputs,  # Replace with validation split
          compute_metrics=compute_metrics
      )
      trainer.train()
      eval_result = trainer.evaluate()
      current_loss = eval_result["loss"]
      if current_loss < best_loss:
          best_loss = current_loss
          best_params = {"lr": lr, "batch_size": bs, "grad_acc_steps": gas, "warmup_steps": ws, "epochs": epochs}
      torch.cuda.empty_cache()

  with open(f"/home/<USER>/projects/qwen3_finetune/best_params_{model_name.split('/')[-1]}.txt", "w") as f:
      f.write(str(best_params))
  print(f"Best parameters for {model_name}: {best_params}, Best loss: {best_loss}")
  ```

### 8. Optimize for Tesla P40
- Enable mixed precision: `fp16=True` for VRAM efficiency.
- Batch size: Start with 8 for 1.7B, 4 for 4B; if OOM, reduce and increase gradient_accumulation_steps.
- Clear GPU memory: `torch.cuda.empty_cache()`.
- Monitor: Run `nvidia-smi` in another terminal (VRAM ~24GB max).
- Save checkpoints: `/home/<USER>/projects/qwen3_finetune/checkpoints`.

### 9. Run and Save
- Save script as `optimize_qwen3.py` and upload:
  ```
  scp optimize_qwen3.py ailab@**********:/home/<USER>/projects/qwen3_finetune/
  ```
- Execute for 1.7B:
  ```
  ssh ailab@**********
  cd /home/<USER>/projects/qwen3_finetune
  source env/bin/activate
  python optimize_qwen3.py
  ```
- Repeat for 4B: Edit `model_name = "Qwen/Qwen3-4B"` in script and rerun.
- Check best params: `/home/<USER>/projects/qwen3_finetune/best_params_Qwen3-1.7B.txt` or `best_params_Qwen3-4B.txt`.
- Save model:
  ```
  model.save_pretrained(f"/home/<USER>/projects/qwen3_finetune/best_model_{model_name.split('/')[-1]}")
  tokenizer.save_pretrained(f"/home/<USER>/projects/qwen3_finetune/best_model_{model_name.split('/')[-1]}")
  ```

## Notes
- **Model Choice**: Start with 1.7B (1.4 GB) for faster testing; 4B (2.6 GB) may perform better but needs smaller batches.
- **Tesla P40**: 24GB VRAM limits batch size; adjust for 4B model carefully.
- **Ollama API**: Assumes `llama3`; adjust model name per API setup.
- **QnA Quality**: Inspect `qna_dataset.csv` for accuracy.
- **Security**: Ensure permissions for files, Ollama API, and GPU access.