---
- name: Deploy Qwen3 LoRA Fine-tuned Model as GGUF to Ollama
  hosts: tesla_p40
  become: false
  gather_facts: true

  vars:
    project_dir: /home/<USER>/projects/qwen3_lora_finetune
    venv_dir: "{{ project_dir }}/env"
    model_name: qwen3-ams-lora
    gguf_file: qwen3_ams_merged.gguf

  tasks:
    - name: Check if project directory exists
      stat:
        path: "{{ project_dir }}"
      register: project_check

    - name: Create project directory if not exists
      file:
        path: "{{ project_dir }}"
        state: directory
        mode: '0755'
      when: not project_check.stat.exists

    - name: Copy GGUF conversion script
      copy:
        src: "./convert_to_gguf.py"
        dest: "{{ project_dir }}/convert_to_gguf.py"
        mode: '0755'

    - name: Copy full pipeline script
      copy:
        src: "./run_full_pipeline.py"
        dest: "{{ project_dir }}/run_full_pipeline.py"
        mode: '0755'

    - name: Copy QnA generation script
      copy:
        src: "./generate_qna_data.py"
        dest: "{{ project_dir }}/generate_qna_data.py"
        mode: '0755'

    - name: Copy updated fine-tuning script
      copy:
        src: "./qwen3_lora_finetune.py"
        dest: "{{ project_dir }}/qwen3_lora_finetune.py"
        mode: '0755'

    - name: Copy AMS data files
      copy:
        src: "./data/"
        dest: "{{ project_dir }}/data/"
        mode: '0644'

    - name: Check if virtual environment exists
      stat:
        path: "{{ venv_dir }}/bin/activate"
      register: venv_check

    - name: Create Python virtual environment
      shell: python3 -m venv {{ venv_dir }}
      when: not venv_check.stat.exists

    - name: Install additional dependencies for GGUF conversion
      shell: "{{ venv_dir }}/bin/pip install {{ item }}"
      loop:
        - "requests"
        - "llama-cpp-python"
        - "numpy"
        - "torch"
        - "transformers"
        - "peft"

    - name: Check if Ollama is installed
      shell: which ollama
      register: ollama_check
      ignore_errors: true

    - name: Install Ollama if not present
      block:
        - name: Download Ollama install script
          get_url:
            url: https://ollama.ai/install.sh
            dest: /tmp/ollama_install.sh
            mode: '0755'

        - name: Install Ollama
          shell: /tmp/ollama_install.sh
          become: true

        - name: Start Ollama service
          systemd:
            name: ollama
            state: started
            enabled: true
          become: true
      when: ollama_check.rc != 0

    - name: Check if merged model exists
      stat:
        path: "{{ project_dir }}/qwen3_ams_merged"
      register: merged_model_check

    - name: Run full pipeline if merged model doesn't exist
      shell: |
        cd {{ project_dir }}
        {{ venv_dir }}/bin/python run_full_pipeline.py
      register: pipeline_result
      when: not merged_model_check.stat.exists

    - name: Display pipeline results
      debug:
        msg: "{{ pipeline_result.stdout }}"
      when: pipeline_result is defined and pipeline_result.stdout is defined

    - name: Check if GGUF file exists
      stat:
        path: "{{ project_dir }}/{{ gguf_file }}"
      register: gguf_check

    - name: Convert to GGUF if not exists
      shell: |
        cd {{ project_dir }}
        {{ venv_dir }}/bin/python convert_to_gguf.py \
          --model-path ./qwen3_ams_merged \
          --output {{ gguf_file }} \
          --quantization q4_0 \
          --model-name {{ model_name }}
      register: gguf_conversion
      when: not gguf_check.stat.exists and merged_model_check.stat.exists

    - name: Display GGUF conversion results
      debug:
        msg: "{{ gguf_conversion.stdout }}"
      when: gguf_conversion is defined and gguf_conversion.stdout is defined

    - name: Check GGUF file size
      stat:
        path: "{{ project_dir }}/{{ gguf_file }}"
      register: gguf_file_stat

    - name: Display GGUF file info
      debug:
        msg: "GGUF file size: {{ (gguf_file_stat.stat.size / 1024 / 1024) | round(1) }} MB"
      when: gguf_file_stat.stat.exists

    - name: Create Ollama Modelfile
      template:
        content: |
          FROM {{ project_dir }}/{{ gguf_file }}

          TEMPLATE """Câu hỏi: {{ "{{ .Prompt }}" }}
          Trả lời: """

          PARAMETER temperature 0.3
          PARAMETER top_p 0.9
          PARAMETER top_k 40
          PARAMETER repeat_penalty 1.1
          PARAMETER stop "Câu hỏi:"
          PARAMETER stop "\n\n"

          SYSTEM """Bạn là trợ lý AI của công ty AMS, được fine-tune với LoRA để trả lời chính xác các câu hỏi về quy định công ty bằng tiếng Việt. Hãy trả lời ngắn gọn, chính xác và hữu ích."""
        dest: "{{ project_dir }}/Modelfile.{{ model_name }}"
        mode: '0644'

    - name: Check if model already exists in Ollama
      shell: ollama list | grep {{ model_name }}
      register: ollama_model_check
      ignore_errors: true

    - name: Remove existing model if present
      shell: ollama rm {{ model_name }}
      when: ollama_model_check.rc == 0
      ignore_errors: true

    - name: Deploy GGUF model to Ollama
      shell: |
        cd {{ project_dir }}
        ollama create {{ model_name }} -f Modelfile.{{ model_name }}
      register: ollama_deploy
      when: gguf_file_stat.stat.exists

    - name: Display Ollama deployment result
      debug:
        msg: "{{ ollama_deploy.stdout }}"
      when: ollama_deploy is defined and ollama_deploy.stdout is defined

    - name: Test deployed model
      shell: |
        timeout 30 ollama run {{ model_name }} "Công ty AMS có địa chỉ ở đâu?"
      register: model_test
      ignore_errors: true

    - name: Display test result
      debug:
        msg: |
          Test Question: Công ty AMS có địa chỉ ở đâu?
          Model Response: {{ model_test.stdout if model_test.rc == 0 else 'Test failed or timeout' }}

    - name: Create deployment summary
      template:
        content: |
          # Qwen3 LoRA GGUF Deployment Summary
          
          Deployment Date: {{ ansible_date_time.iso8601 }}
          Host: {{ inventory_hostname }}
          
          ## Model Information
          - Base Model: Qwen/Qwen3-1.7B
          - Fine-tuning Method: LoRA (Low-Rank Adaptation)
          - Format: GGUF ({{ (gguf_file_stat.stat.size / 1024 / 1024) | round(1) }} MB)
          - Quantization: Q4_0
          - Ollama Model Name: {{ model_name }}
          
          ## Deployment Status
          - GGUF Conversion: {{ 'Success' if gguf_file_stat.stat.exists else 'Failed' }}
          - Ollama Deployment: {{ 'Success' if ollama_deploy.rc == 0 else 'Failed' }}
          - Model Test: {{ 'Success' if model_test.rc == 0 else 'Failed' }}
          
          ## Usage
          ```bash
          ollama run {{ model_name }} "Your question about AMS"
          ```
          
          ## Test Results
          Question: Công ty AMS có địa chỉ ở đâu?
          Response: {{ model_test.stdout if model_test.rc == 0 else 'Test failed' }}
          
          ## Files Created
          - {{ project_dir }}/{{ gguf_file }}
          - {{ project_dir }}/Modelfile.{{ model_name }}
          - {{ project_dir }}/generated_qna_data.json
          - {{ project_dir }}/training_data.json
          
          ## Performance Benefits (GGUF vs Original)
          - File Size: ~1-2GB vs ~3-4GB (50-60% reduction)
          - Memory Usage: ~2-4GB vs ~6-8GB (50-60% reduction)
          - Inference Speed: 2-3x faster
          - CPU Compatible: Yes (with reduced performance)
        dest: "{{ project_dir }}/deployment_summary.md"
        mode: '0644'

    - name: Display final deployment summary
      debug:
        msg: |
          🎉 GGUF Deployment Completed!
          
          ✅ Model: {{ model_name }}
          ✅ Format: GGUF ({{ (gguf_file_stat.stat.size / 1024 / 1024) | round(1) }} MB)
          ✅ Deployment: {{ 'Success' if ollama_deploy.rc == 0 else 'Failed' }}
          ✅ Test: {{ 'Success' if model_test.rc == 0 else 'Failed' }}
          
          🚀 Usage:
          ollama run {{ model_name }} "Câu hỏi về AMS"
          
          📊 Benefits:
          - 50-60% smaller file size
          - 2-3x faster inference
          - Lower memory usage
          - CPU compatible
          
          📁 Summary: {{ project_dir }}/deployment_summary.md
