---
- name: Qwen3 <PERSON><PERSON> Fine-tuning Production Pipeline Deployment
  hosts: tesla_p40
  become: false
  gather_facts: true

  vars:
    project_dir: /home/<USER>/projects/qwen3_lora_finetune
    venv_dir: "{{ project_dir }}/env"
    model_name: qwen3-ams-lora
    ollama_api_url: "http://**********:11434"

  tasks:
    - name: Check if project directory exists
      stat:
        path: "{{ project_dir }}"
      register: project_check

    - name: Create project directory if not exists
      file:
        path: "{{ project_dir }}"
        state: directory
        mode: '0755'
      when: not project_check.stat.exists

    - name: Copy GGUF conversion script
      copy:
        src: "./convert_to_gguf.py"
        dest: "{{ project_dir }}/convert_to_gguf.py"
        mode: '0755'

    - name: Copy full pipeline script
      copy:
        src: "./run_full_pipeline.py"
        dest: "{{ project_dir }}/run_full_pipeline.py"
        mode: '0755'

    - name: Copy QnA generation script
      copy:
        src: "./generate_qna_data.py"
        dest: "{{ project_dir }}/generate_qna_data.py"
        mode: '0755'

    - name: <PERSON><PERSON> updated fine-tuning script
      copy:
        src: "./qwen3_lora_finetune.py"
        dest: "{{ project_dir }}/qwen3_lora_finetune.py"
        mode: '0755'

    - name: Copy AMS data files
      copy:
        src: "./data/"
        dest: "{{ project_dir }}/data/"
        mode: '0644'

    - name: Check if virtual environment exists
      stat:
        path: "{{ venv_dir }}/bin/activate"
      register: venv_check

    - name: Create Python virtual environment
      shell: python3 -m venv {{ venv_dir }}
      when: not venv_check.stat.exists

    - name: Install Python dependencies
      shell: "{{ venv_dir }}/bin/pip install {{ item }}"
      loop:
        - "requests"
        - "torch"
        - "transformers>=4.35.0"
        - "peft"
        - "datasets"
        - "accelerate"
        - "pynvml"

    - name: Check if Ollama is installed
      shell: which ollama
      register: ollama_check
      ignore_errors: true

    - name: Install Ollama if not present
      block:
        - name: Download Ollama install script
          get_url:
            url: https://ollama.ai/install.sh
            dest: /tmp/ollama_install.sh
            mode: '0755'

        - name: Install Ollama
          shell: /tmp/ollama_install.sh
          become: true

        - name: Start Ollama service
          systemd:
            name: ollama
            state: started
            enabled: true
          become: true
      when: ollama_check.rc != 0

    - name: Run complete pipeline
      shell: |
        cd {{ project_dir }}
        export OLLAMA_HOST={{ ollama_api_url }}
        export PATH={{ venv_dir }}/bin:$PATH
        {{ venv_dir }}/bin/python run_full_pipeline.py
      register: pipeline_result
      async: 3600  # 1 hour timeout
      poll: 30     # Check every 30 seconds

    - name: Display pipeline results
      debug:
        msg: "{{ pipeline_result.stdout_lines[-20:] if pipeline_result.stdout_lines else 'No output' }}"

    - name: Check pipeline success
      fail:
        msg: "Pipeline failed with return code {{ pipeline_result.rc }}"
      when: pipeline_result.rc != 0

    - name: Check generated files
      stat:
        path: "{{ project_dir }}/{{ item }}"
      register: file_checks
      loop:
        - "generated_qna_data.json"
        - "training_data.json"
        - "qwen3_ams_merged.gguf"
        - "pipeline_report.md"

    - name: Display file status
      debug:
        msg: "{{ item.item }}: {{ 'Found' if item.stat.exists else 'Missing' }}"
      loop: "{{ file_checks.results }}"

    - name: Test model deployment
      uri:
        url: "{{ ollama_api_url }}/api/generate"
        method: POST
        body_format: json
        body:
          model: "{{ model_name }}"
          prompt: "Công ty AMS có địa chỉ ở đâu?"
          stream: false
          options:
            temperature: 0.3
            top_p: 0.9
            num_predict: 150
        timeout: 60
      register: model_test
      ignore_errors: true

    - name: Display test result
      debug:
        msg: |
          Test Question: Công ty AMS có địa chỉ ở đâu?
          Model Response: {{ model_test.json.response if model_test.status == 200 else 'Test failed' }}
          Status: {{ 'Success' if model_test.status == 200 else 'Failed' }}

    - name: Copy pipeline report to local
      fetch:
        src: "{{ project_dir }}/pipeline_report.md"
        dest: "./pipeline_report_{{ inventory_hostname }}.md"
        flat: yes
      ignore_errors: true

    - name: Display final deployment summary
      debug:
        msg: |
          🎉 Qwen3 LoRA Pipeline Deployment Completed!

          📊 Results:
          ✅ Pipeline: {{ 'Success' if pipeline_result.rc == 0 else 'Failed' }}
          ✅ Model Test: {{ 'Success' if model_test.status == 200 else 'Failed' }}

          🚀 Usage:
          ollama run {{ model_name }} "Câu hỏi về AMS"

          📁 Reports:
          - Remote: {{ project_dir }}/pipeline_report.md
          - Local: ./pipeline_report_{{ inventory_hostname }}.md

          💡 Benefits:
          - LoRA: 10-100x fewer parameters
          - GGUF: 50-60% smaller size
          - Ollama: Easy deployment
