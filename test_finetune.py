#!/usr/bin/env python3
"""
Script test đơn giản để kiểm tra fine-tuning script
"""
import sys
import os
import subprocess
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_imports():
    """Test các imports cần thiết"""
    logger.info("🔍 Testing imports...")
    
    try:
        import torch
        logger.info(f"✅ PyTorch: {torch.__version__}")
        logger.info(f"✅ CUDA available: {torch.cuda.is_available()}")
        if torch.cuda.is_available():
            logger.info(f"✅ CUDA devices: {torch.cuda.device_count()}")
            logger.info(f"✅ Current device: {torch.cuda.current_device()}")
    except Exception as e:
        logger.error(f"❌ PyTorch error: {e}")
        return False
    
    try:
        import transformers
        logger.info(f"✅ Transformers: {transformers.__version__}")
    except Exception as e:
        logger.error(f"❌ Transformers error: {e}")
        return False
    
    try:
        import peft
        logger.info(f"✅ PEFT: {peft.__version__}")
    except Exception as e:
        logger.error(f"❌ PEFT error: {e}")
        return False
    
    try:
        import json
        with open('training_data.json', 'r') as f:
            data = json.load(f)
        logger.info(f"✅ Training data: {len(data)} samples")
    except Exception as e:
        logger.error(f"❌ Training data error: {e}")
        return False
    
    return True

def test_model_loading():
    """Test model loading"""
    logger.info("🔍 Testing model loading...")
    
    try:
        from transformers import AutoTokenizer, AutoModelForCausalLM
        
        model_name = "Qwen/Qwen3-1.7B"
        logger.info(f"Loading tokenizer for {model_name}...")
        tokenizer = AutoTokenizer.from_pretrained(model_name, trust_remote_code=True)
        logger.info("✅ Tokenizer loaded successfully")
        
        logger.info(f"Loading model for {model_name}...")
        model = AutoModelForCausalLM.from_pretrained(
            model_name,
            trust_remote_code=True,
            torch_dtype="auto",
            device_map="auto"
        )
        logger.info("✅ Model loaded successfully")
        
        # Test a simple forward pass
        inputs = tokenizer("Hello", return_tensors="pt")
        with torch.no_grad():
            outputs = model(**inputs)
        logger.info("✅ Model forward pass successful")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Model loading error: {e}")
        return False

def main():
    """Main test function"""
    logger.info("🧪 Starting fine-tuning environment test")
    
    # Test 1: Imports
    if not test_imports():
        logger.error("❌ Import test failed")
        return False
    
    # Test 2: Model loading
    if not test_model_loading():
        logger.error("❌ Model loading test failed")
        return False
    
    logger.info("🎉 All tests passed! Fine-tuning environment is ready.")
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
