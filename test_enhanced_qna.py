#!/usr/bin/env python3
"""
Test script cho enhanced QnA generation
"""
import sys
import os
import json
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_enhanced_qna():
    """Test enhanced QnA generation"""
    logger.info("🧪 Testing Enhanced QnA Generation")
    
    try:
        # Import the enhanced functions
        from generate_qna_data import (
            generate_qna_from_text, 
            check_ollama_connection,
            read_data_files
        )
        
        # Test Ollama connection
        if not check_ollama_connection():
            logger.error("❌ Cannot connect to Ollama")
            return False
        
        # Read a sample data file
        files_content = read_data_files()
        if not files_content:
            logger.error("❌ No data files found")
            return False
        
        # Test with first file
        filename, content = list(files_content.items())[0]
        logger.info(f"📄 Testing with file: {filename}")
        
        # Generate QnA with enhanced method
        qna_results = generate_qna_from_text(content[:2000], filename)  # Use first 2000 chars
        
        if not qna_results:
            logger.error("❌ No QnA generated")
            return False
        
        # Analyze results
        logger.info(f"✅ Generated {len(qna_results)} QnA pairs")
        
        # Show quality statistics
        types = {}
        scores = []
        for item in qna_results:
            qna_type = item.get('qna_type', 'unknown')
            types[qna_type] = types.get(qna_type, 0) + 1
            scores.append(item.get('quality_score', 0))
        
        avg_score = sum(scores) / len(scores) if scores else 0
        high_quality = sum(1 for score in scores if score > 0.7)
        
        logger.info("📊 Quality Analysis:")
        logger.info(f"  - Average quality score: {avg_score:.2f}")
        logger.info(f"  - High quality pairs (>0.7): {high_quality}/{len(qna_results)}")
        logger.info(f"  - QnA types: {types}")
        
        # Show sample QnA
        logger.info("\n📝 Sample QnA pairs:")
        for i, item in enumerate(qna_results[:3]):  # Show first 3
            logger.info(f"\n{i+1}. Type: {item.get('qna_type', 'unknown')}")
            logger.info(f"   Score: {item.get('quality_score', 0):.2f}")
            logger.info(f"   Q: {item['question'][:100]}...")
            logger.info(f"   A: {item['answer'][:150]}...")
        
        # Save test results
        with open('test_qna_results.json', 'w', encoding='utf-8') as f:
            json.dump(qna_results, f, ensure_ascii=False, indent=2)
        
        logger.info(f"\n✅ Test completed! Results saved to test_qna_results.json")
        return True
        
    except Exception as e:
        logger.error(f"❌ Test failed: {e}")
        return False

def main():
    """Main function"""
    success = test_enhanced_qna()
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
