#!/usr/bin/env python3
"""
Test script for fine-tuned Qwen3-1.7B model with AMS data
"""
import torch
from transformers import AutoModelForCausalLM, AutoTokenizer
import json

def test_model():
    print("🧪 Testing fine-tuned Qwen3-1.7B model...")
    
    try:
        model_path = "./qwen3_ams_merged"
        
        # Load tokenizer and model
        tokenizer = AutoTokenizer.from_pretrained(model_path, trust_remote_code=True)
        model = AutoModelForCausalLM.from_pretrained(
            model_path, 
            torch_dtype=torch.float16, 
            device_map="auto", 
            trust_remote_code=True
        )
        
        print(f"✅ Model loaded from: {model_path}")
        
        # Test questions about AMS
        test_questions = [
            "Câu hỏi: Công ty AMS có địa chỉ ở đâu? Trả lời:",
            "Câu hỏi: Thời gian thử việc tại AMS là bao lâu? Trả lời:",
            "Câu hỏi: Nhân viên được nghỉ những ngày lễ nào? Tr<PERSON> lời:",
            "Câu hỏi: Thu nhập thử việc như thế nào? Tr<PERSON> lời:",
            "Câu hỏi: Quy định về trang phục tại AMS? Trả lời:",
            "Câu hỏi: Điện thoại công ty AMS là gì? Trả lời:",
            "Câu hỏi: Website của AMS là gì? Trả lời:",
            "Câu hỏi: Giờ làm việc của AMS? Trả lời:",
            "Câu hỏi: Chế độ bảo hiểm tại AMS? Trả lời:",
            "Câu hỏi: Quy trình nghỉ phép tại AMS? Trả lời:"
        ]
        
        results = []
        
        for i, question in enumerate(test_questions, 1):
            print(f"\n📝 Test {i}/10: {question[:50]}...")
            
            inputs = tokenizer(question, return_tensors="pt").to(model.device)
            
            with torch.no_grad():
                outputs = model.generate(
                    **inputs, 
                    max_new_tokens=150, 
                    do_sample=True, 
                    temperature=0.3, 
                    top_p=0.9,
                    pad_token_id=tokenizer.eos_token_id,
                    eos_token_id=tokenizer.eos_token_id,
                    repetition_penalty=1.1
                )
            
            response = tokenizer.decode(outputs[0], skip_special_tokens=True)
            
            # Extract only the answer part
            if "Trả lời:" in response:
                answer_part = response.split("Trả lời:", 1)[1].strip()
                # Clean up repetitive text
                if "Câu hỏi:" in answer_part:
                    answer_part = answer_part.split("Câu hỏi:")[0].strip()
                if "Human:" in answer_part:
                    answer_part = answer_part.split("Human:")[0].strip()
            else:
                answer_part = response
            
            print(f"✅ Answer: {answer_part[:200]}...")
            
            results.append({
                'question': question,
                'full_response': response,
                'clean_answer': answer_part
            })
        
        # Save results
        with open('./final_test_results.json', 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        
        print(f"\n🎉 Testing completed! Results saved to final_test_results.json")
        
        # Summary
        print("\n📊 SUMMARY:")
        print("="*60)
        for i, result in enumerate(results, 1):
            question_short = result['question'].replace("Câu hỏi: ", "").replace(" Trả lời:", "")
            answer_short = result['clean_answer'][:100] + "..." if len(result['clean_answer']) > 100 else result['clean_answer']
            print(f"{i:2d}. {question_short}")
            print(f"    → {answer_short}")
            print()
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_model()
