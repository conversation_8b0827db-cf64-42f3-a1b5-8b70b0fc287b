{"model": {"model_name": "Qwen/Qwen2-7B", "model_id": null, "device_map": "cuda:0", "torch_dtype": "float16", "trust_remote_code": true}, "data": {"data_dir": "./data", "qna_dataset_path": "./qna_dataset.csv", "max_length": 512, "test_size": 0.2, "min_question_length": 10, "max_question_length": 500, "min_answer_length": 10, "max_answer_length": 1000}, "training": {"output_dir": "./checkpoints_4B", "logging_dir": "./logs_4B", "num_train_epochs": 3, "per_device_train_batch_size": 4, "per_device_eval_batch_size": 4, "gradient_accumulation_steps": 2, "learning_rate": 2e-05, "weight_decay": 0.01, "warmup_steps": 100, "logging_steps": 10, "eval_steps": 100, "save_steps": 500, "save_total_limit": 3, "evaluation_strategy": "steps", "load_best_model_at_end": true, "metric_for_best_model": "eval_loss", "greater_is_better": false, "fp16": true, "dataloader_num_workers": 0, "remove_unused_columns": false, "report_to": "none", "seed": 42}, "hyperparameter_search": {"learning_rates": [1e-05, 2e-05, 5e-05], "batch_sizes": [2, 4], "gradient_accumulation_steps": [1, 2, 4], "warmup_steps": [0, 100, 200], "num_epochs": [3, 5], "max_trials": 15}, "experiment_name": "qwen3_4B_finetune", "description": "Fine-tuning Qwen3 4B on Vietnamese AMS employee handbook QnA for Tesla P40"}