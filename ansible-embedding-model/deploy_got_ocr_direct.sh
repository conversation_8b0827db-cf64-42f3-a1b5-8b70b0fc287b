#!/bin/bash

# Script để triển khai mô hình GOT-OCR-2.0-hf trực tiếp với Transformers

# Hiển thị hướng dẫn sử dụng
show_help() {
    echo "Sử dụng: $0 [options]"
    echo ""
    echo "Script cài đặt GOT-OCR-2.0-hf trực tiếp với Transformers API:"
    echo "- Model name: got-ocr-2"
    echo "- API Server: FastAPI với endpoint /api/ocr"
    echo "- Port: 8000 (có thể thay đổi)"
    echo ""
    echo "Options:"
    echo "  -h, --help                   Hi<PERSON>n thị hướng dẫn này"
    echo "  -p, --port PORT              Port cho API server (mặc định: 8000)"
    echo ""
    echo "Ví dụ:"
    echo "  $0                           # Cài đặt với cấu hình mặc định"
    echo "  $0 --port 8080              # Sử dụng port 8080"
    exit 0
}

# Xử lý tham số dòng lệnh
EXTRA_VARS=""

while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            ;;
        -p|--port)
            EXTRA_VARS="$EXTRA_VARS service_port=$2"
            shift 2
            ;;
        *)
            echo "Tham số không hợp lệ: $1"
            show_help
            ;;
    esac
done

# Kiểm tra xem Ansible đã được cài đặt chưa
if ! command -v ansible &> /dev/null; then
    echo "Ansible chưa được cài đặt. Vui lòng cài đặt Ansible trước khi chạy script này."
    exit 1
fi

# Kiểm tra xem inventory.ini có tồn tại không
if [ ! -f "inventory.ini" ]; then
    echo "File inventory.ini không tồn tại. Vui lòng tạo file này với thông tin máy chủ đích."
    exit 1
fi

echo "Bắt đầu triển khai GOT-OCR-2.0-hf với Transformers API..."
echo "Cấu hình:"
echo "- Model: stepfun-ai/GOT-OCR-2.0-hf"
echo "- API Framework: FastAPI"
echo "- Port: 8000 (mặc định)"
echo "- Service: Systemd service"
echo ""
echo "Lưu ý: Model sẽ chạy trực tiếp với Transformers, không cần convert GGUF."
echo "Quá trình cài đặt có thể mất 15-30 phút."

# Chạy playbook
if [ -z "$EXTRA_VARS" ]; then
    ansible-playbook -i inventory.ini got_ocr_direct_playbook.yml --ask-become-pass
else
    ansible-playbook -i inventory.ini got_ocr_direct_playbook.yml -e "$EXTRA_VARS" --ask-become-pass
fi

# Kiểm tra kết quả
if [ $? -eq 0 ]; then
    echo ""
    echo "=== TRIỂN KHAI THÀNH CÔNG ==="
    echo "GOT-OCR-2.0-hf API Server đã được cài đặt thành công!"
    echo ""
    echo "Thông tin service:"
    echo "- API URL: http://10.124.1.8:8000"
    echo "- Health Check: http://10.124.1.8:8000/health"
    echo "- OCR Endpoint: http://10.124.1.8:8000/api/ocr"
    echo ""
    echo "Cách sử dụng:"
    echo "# Kiểm tra health"
    echo "curl http://10.124.1.8:8000/health"
    echo ""
    echo "# OCR với base64 image"
    echo "curl -X POST http://10.124.1.8:8000/api/ocr \\"
    echo "  -H 'Content-Type: application/json' \\"
    echo "  -d '{"
    echo "    \"images\": [\"base64_encoded_image_here\"],"
    echo "    \"prompt\": \"Extract text from this image\""
    echo "  }'"
    echo ""
    echo "Service được quản lý bởi systemd:"
    echo "- Start: sudo systemctl start got-ocr-api"
    echo "- Stop: sudo systemctl stop got-ocr-api"
    echo "- Status: sudo systemctl status got-ocr-api"
    echo "- Logs: sudo journalctl -u got-ocr-api -f"
else
    echo "Triển khai GOT-OCR-2.0-hf thất bại. Vui lòng kiểm tra lỗi ở trên."
    exit 1
fi
