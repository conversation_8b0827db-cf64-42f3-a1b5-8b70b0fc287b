---
- name: <PERSON><PERSON> Modelfile for GOT-OCR vision model
  copy:
    dest: "{{ model_dir }}/Modelfile"
    content: |
      FROM {{ model_dir }}/{{ model_name }}.gguf
      TEMPLATE """{{ if .System }}{{ .System }}{{ end }}{{ if .Prompt }}<|im_start|>user
      {{ .Prompt }}<|im_end|>
      <|im_start|>assistant
      {{ end }}{{ .Response }}<|im_end|>"""
      PARAMETER stop "<|im_end|>"
      PARAMETER num_ctx {{ context_size }}
      PARAMETER temperature 0.0
      PARAMETER top_p 1.0
      PARAMETER repeat_penalty 1.0
      SYSTEM "You are GOT-OCR-2.0, a powerful OCR model. You can extract text from images, recognize formatted documents, tables, charts, mathematical formulas, and even sheet music. Provide accurate text extraction and maintain formatting when possible."
    mode: '0644'

- name: Check if vision model is already installed in Ollama
  shell: ollama list | grep {{ model_name }}
  register: ollama_model_check
  ignore_errors: yes
  changed_when: false

- name: Create vision model in Ollama
  shell: cd {{ model_dir }} && ollama create {{ model_name }} -f Modelfile
  when: ollama_model_check.rc != 0

- name: Test the vision model with a simple text prompt
  shell: |
    source {{ model_dir }}_venv/bin/activate &&
    python3 -c "
    import requests
    import json

    # Test with a simple text prompt first
    response = requests.post('http://localhost:11434/api/generate',
                           json={'model': '{{ model_name }}',
                                'prompt': 'Hello, can you help me with OCR tasks?',
                                'stream': False})

    if response.status_code == 200:
        result = response.json()
        print(f'Model response: {result.get(\"response\", \"No response\")}')
    else:
        print(f'Error: {response.status_code} - {response.text}')
    "
  args:
    executable: /bin/bash
  register: vision_test
  changed_when: false
  ignore_errors: yes

- name: Show vision model test result
  debug:
    var: vision_test.stdout

- name: Display usage instructions
  debug:
    msg: |
      GOT-OCR-2.0-hf model has been installed successfully!

      This is a vision-language model for OCR tasks. To use it:

      1. For text OCR from images, you can use the Ollama API with image input
      2. The model supports various OCR tasks including:
         - Plain document OCR
         - Scene text OCR
         - Formatted document OCR
         - Tables, charts, mathematical formulas
         - Sheet music OCR

      Example usage with curl:
      curl -X POST http://localhost:11434/api/generate \
        -H "Content-Type: application/json" \
        -d '{
          "model": "{{ model_name }}",
          "prompt": "Extract text from this image",
          "images": ["base64_encoded_image_here"]
        }'
