---
- name: Enable and start GOT-OCR API service
  systemd:
    name: got-ocr-api
    enabled: yes
    state: started

- name: Wait for service to be ready
  wait_for:
    port: "{{ service_port }}"
    host: "0.0.0.0"
    delay: 10
    timeout: 120

- name: Test GOT-OCR API health endpoint
  uri:
    url: "http://localhost:{{ service_port }}/health"
    method: GET
  register: health_check
  retries: 3
  delay: 5

- name: Display service status
  debug:
    msg: |
      GOT-OCR-2.0 API Service đã được cài đặt thành công!
      
      Service URL: http://{{ ansible_default_ipv4.address }}:{{ service_port }}
      Health Check: http://{{ ansible_default_ipv4.address }}:{{ service_port }}/health
      API Endpoint: http://{{ ansible_default_ipv4.address }}:{{ service_port }}/api/ocr
      
      <PERSON><PERSON> kiểm tra service:
      curl http://{{ ansible_default_ipv4.address }}:{{ service_port }}/health
      
      Đ<PERSON> sử dụng OCR:
      curl -X POST http://{{ ansible_default_ipv4.address }}:{{ service_port }}/api/ocr \
        -H "Content-Type: application/json" \
        -d '{
          "images": ["base64_encoded_image_here"],
          "prompt": "Extract text from this image"
        }'

- name: Show service logs (last 10 lines)
  shell: journalctl -u got-ocr-api -n 10 --no-pager
  register: service_logs

- name: Display service logs
  debug:
    var: service_logs.stdout_lines
