---
- name: Create GOT-OCR API server script
  copy:
    dest: "{{ model_dir }}/got_ocr_server.py"
    content: |
      #!/usr/bin/env python3
      import torch
      from transformers import AutoProcessor, AutoModelForImageTextToText
      from fastapi import FastAPI, HTTPException
      from pydantic import BaseModel
      from PIL import Image
      import base64
      import io
      import uvicorn
      from typing import List, Optional
      import logging

      # Setup logging
      logging.basicConfig(level=logging.INFO)
      logger = logging.getLogger(__name__)

      app = FastAPI(title="GOT-OCR-2.0 API", version="1.0.0")

      # Global variables for model and processor
      model = None
      processor = None
      device = None

      class OCRRequest(BaseModel):
          images: List[str]  # Base64 encoded images
          prompt: Optional[str] = "Extract text from this image"
          max_new_tokens: Optional[int] = 4096
          temperature: Optional[float] = 0.0
          format: Optional[bool] = False
          multi_page: Optional[bool] = False
          crop_to_patches: Optional[bool] = False
          max_patches: Optional[int] = 3

      class OCRResponse(BaseModel):
          text: str
          success: bool
          error: Optional[str] = None

      @app.on_event("startup")
      async def load_model():
          global model, processor, device
          try:
              logger.info("Loading GOT-OCR-2.0-hf model...")
              device = "cuda" if torch.cuda.is_available() else "cpu"
              logger.info(f"Using device: {device}")
              
              model = AutoModelForImageTextToText.from_pretrained(
                  "{{ model_dir }}", 
                  device_map=device,
                  torch_dtype=torch.float16 if device == "cuda" else torch.float32
              )
              processor = AutoProcessor.from_pretrained("{{ model_dir }}")
              
              logger.info("Model loaded successfully!")
          except Exception as e:
              logger.error(f"Failed to load model: {e}")
              raise e

      def decode_base64_image(base64_string: str) -> Image.Image:
          """Decode base64 string to PIL Image"""
          try:
              image_data = base64.b64decode(base64_string)
              image = Image.open(io.BytesIO(image_data))
              return image
          except Exception as e:
              raise HTTPException(status_code=400, detail=f"Invalid image data: {e}")

      @app.post("/api/ocr", response_model=OCRResponse)
      async def perform_ocr(request: OCRRequest):
          try:
              if not request.images:
                  raise HTTPException(status_code=400, detail="No images provided")
              
              # Decode images
              images = []
              for img_b64 in request.images:
                  image = decode_base64_image(img_b64)
                  images.append(image)
              
              # Process single or multiple images
              if len(images) == 1:
                  inputs = processor(
                      images[0], 
                      return_tensors="pt",
                      format=request.format,
                      crop_to_patches=request.crop_to_patches,
                      max_patches=request.max_patches
                  ).to(device)
              else:
                  inputs = processor(
                      images, 
                      return_tensors="pt",
                      multi_page=request.multi_page,
                      format=request.format
                  ).to(device)
              
              # Generate text
              with torch.no_grad():
                  generate_ids = model.generate(
                      **inputs,
                      do_sample=False if request.temperature == 0.0 else True,
                      temperature=request.temperature if request.temperature > 0.0 else None,
                      tokenizer=processor.tokenizer,
                      stop_strings="<|im_end|>",
                      max_new_tokens=request.max_new_tokens,
                  )
              
              # Decode response
              response_text = processor.decode(
                  generate_ids[0, inputs["input_ids"].shape[1]:], 
                  skip_special_tokens=True
              )
              
              return OCRResponse(text=response_text, success=True)
              
          except Exception as e:
              logger.error(f"OCR processing failed: {e}")
              return OCRResponse(text="", success=False, error=str(e))

      @app.get("/health")
      async def health_check():
          return {"status": "healthy", "model_loaded": model is not None}

      @app.get("/")
      async def root():
          return {
              "message": "GOT-OCR-2.0 API Server",
              "endpoints": {
                  "ocr": "/api/ocr",
                  "health": "/health"
              }
          }

      if __name__ == "__main__":
          uvicorn.run(app, host="0.0.0.0", port={{ service_port }})
    mode: '0755'

- name: Install additional Python dependencies for API server
  pip:
    name:
      - fastapi
      - uvicorn
      - python-multipart
    virtualenv: "{{ model_dir }}_venv"
    state: present

- name: Create systemd service file for GOT-OCR
  copy:
    dest: "/etc/systemd/system/got-ocr-api.service"
    content: |
      [Unit]
      Description=GOT-OCR-2.0 API Server
      After=network.target

      [Service]
      Type=simple
      User=root
      WorkingDirectory={{ model_dir }}
      Environment=PATH={{ model_dir }}_venv/bin
      ExecStart={{ model_dir }}_venv/bin/python {{ model_dir }}/got_ocr_server.py
      Restart=always
      RestartSec=10

      [Install]
      WantedBy=multi-user.target
    mode: '0644'

- name: Reload systemd daemon
  systemd:
    daemon_reload: yes
