{"model": {"model_name": "microsoft/DialoGPT-medium", "model_id": null, "device_map": "auto", "torch_dtype": "float32", "trust_remote_code": true}, "data": {"data_dir": "./data", "qna_dataset_path": "./qna_dataset.csv", "max_length": 256, "test_size": 0.3, "min_question_length": 10, "max_question_length": 500, "min_answer_length": 10, "max_answer_length": 1000}, "training": {"output_dir": "./checkpoints_local", "logging_dir": "./logs_local", "num_train_epochs": 1, "per_device_train_batch_size": 2, "per_device_eval_batch_size": 2, "gradient_accumulation_steps": 1, "learning_rate": 5e-05, "weight_decay": 0.01, "warmup_steps": 10, "logging_steps": 5, "eval_steps": 20, "save_steps": 50, "save_total_limit": 3, "evaluation_strategy": "steps", "load_best_model_at_end": true, "metric_for_best_model": "eval_loss", "greater_is_better": false, "fp16": false, "dataloader_num_workers": 0, "remove_unused_columns": false, "report_to": "none", "seed": 42}, "hyperparameter_search": {"learning_rates": [2e-05, 5e-05], "batch_sizes": [2, 4], "gradient_accumulation_steps": [1, 2], "warmup_steps": [0, 10], "num_epochs": [1, 2], "max_trials": 4}, "experiment_name": "qwen3_local_test", "description": "Local testing configuration"}