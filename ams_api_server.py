#!/usr/bin/env python3
"""
AMS AI Assistant API Server
Fine-tuned Qwen3-1.7B model serving API for AMS company questions
"""
from flask import Flask, request, jsonify
import torch
from transformers import AutoModelForCausalLM, AutoTokenizer
import logging
import threading
import time

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = Flask(__name__)

# Global model and tokenizer
model = None
tokenizer = None
model_loaded = False

def load_model():
    """Load the fine-tuned model"""
    global model, tokenizer, model_loaded
    
    try:
        logger.info("🚀 Loading fine-tuned Qwen3-1.7B model...")
        
        model_path = "./qwen3_ams_merged"
        
        tokenizer = AutoTokenizer.from_pretrained(model_path, trust_remote_code=True)
        model = AutoModelForCausalLM.from_pretrained(
            model_path, 
            torch_dtype=torch.float16, 
            device_map="auto", 
            trust_remote_code=True
        )
        
        model.eval()
        model_loaded = True
        
        logger.info("✅ Model loaded successfully!")
        
    except Exception as e:
        logger.error(f"❌ Failed to load model: {e}")
        model_loaded = False

def generate_response(question):
    """Generate response for a question"""
    if not model_loaded:
        return "❌ Model not loaded"
    
    try:
        # Format question
        if not question.startswith("Câu hỏi:"):
            formatted_question = f"Câu hỏi: {question} Trả lời:"
        else:
            formatted_question = question if question.endswith("Trả lời:") else f"{question} Trả lời:"
        
        # Tokenize
        inputs = tokenizer(formatted_question, return_tensors="pt").to(model.device)
        
        # Generate
        with torch.no_grad():
            outputs = model.generate(
                **inputs, 
                max_new_tokens=150, 
                do_sample=True, 
                temperature=0.3, 
                top_p=0.9,
                pad_token_id=tokenizer.eos_token_id,
                eos_token_id=tokenizer.eos_token_id,
                repetition_penalty=1.1
            )
        
        # Decode
        response = tokenizer.decode(outputs[0], skip_special_tokens=True)
        
        # Extract answer
        if "Trả lời:" in response:
            answer = response.split("Trả lời:", 1)[1].strip()
            # Clean up
            if "Câu hỏi:" in answer:
                answer = answer.split("Câu hỏi:")[0].strip()
            if "Human:" in answer:
                answer = answer.split("Human:")[0].strip()
        else:
            answer = response
        
        return answer
        
    except Exception as e:
        logger.error(f"❌ Generation error: {e}")
        return f"❌ Lỗi: {str(e)}"

@app.route('/', methods=['GET'])
def home():
    """Home page"""
    return jsonify({
        "service": "AMS AI Assistant",
        "model": "Qwen3-1.7B Fine-tuned with LoRA",
        "status": "ready" if model_loaded else "loading",
        "description": "AI Assistant for AMS company questions in Vietnamese",
        "usage": {
            "endpoint": "/ask",
            "method": "POST",
            "body": {"question": "Your question about AMS"}
        }
    })

@app.route('/ask', methods=['POST'])
def ask():
    """Ask a question to the AI assistant"""
    try:
        data = request.get_json()
        
        if not data or 'question' not in data:
            return jsonify({
                "error": "Missing 'question' in request body"
            }), 400
        
        question = data['question'].strip()
        
        if not question:
            return jsonify({
                "error": "Question cannot be empty"
            }), 400
        
        logger.info(f"📝 Question: {question}")
        
        # Generate response
        answer = generate_response(question)
        
        logger.info(f"✅ Answer: {answer[:100]}...")
        
        return jsonify({
            "question": question,
            "answer": answer,
            "model": "Qwen3-1.7B-AMS-LoRA",
            "timestamp": time.time()
        })
        
    except Exception as e:
        logger.error(f"❌ API error: {e}")
        return jsonify({
            "error": str(e)
        }), 500

@app.route('/health', methods=['GET'])
def health():
    """Health check"""
    return jsonify({
        "status": "healthy" if model_loaded else "loading",
        "model_loaded": model_loaded,
        "gpu_available": torch.cuda.is_available(),
        "gpu_name": torch.cuda.get_device_name(0) if torch.cuda.is_available() else None
    })

@app.route('/examples', methods=['GET'])
def examples():
    """Example questions"""
    return jsonify({
        "examples": [
            "Công ty AMS có địa chỉ ở đâu?",
            "Thời gian thử việc tại AMS là bao lâu?",
            "Nhân viên được nghỉ những ngày lễ nào?",
            "Thu nhập thử việc như thế nào?",
            "Quy định về trang phục tại AMS?",
            "Điện thoại công ty AMS là gì?",
            "Website của AMS là gì?",
            "Giờ làm việc của AMS?",
            "Chế độ bảo hiểm tại AMS?",
            "Quy trình nghỉ phép tại AMS?"
        ]
    })

if __name__ == '__main__':
    # Load model in background
    model_thread = threading.Thread(target=load_model)
    model_thread.start()
    
    # Start Flask app
    logger.info("🌐 Starting AMS AI Assistant API Server...")
    logger.info("📡 API will be available at: http://localhost:5000")
    logger.info("📖 Documentation: http://localhost:5000")
    logger.info("🧪 Health check: http://localhost:5000/health")
    logger.info("💡 Examples: http://localhost:5000/examples")
    
    app.run(host='0.0.0.0', port=5000, debug=False)
