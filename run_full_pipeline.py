#!/usr/bin/env python3
"""
Script chạy toàn bộ pipeline:
1. Generate QnA data từ files trong thư mục data
2. Fine-tune Qwen3 với LoRA
3. Convert sang GGUF và deploy lên Ollama
"""
import os
import sys
import logging
import subprocess
import time
from pathlib import Path

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('full_pipeline.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

def run_step(step_name, command, check_files=None):
    """Chạy một bước trong pipeline"""
    logger.info(f"🚀 Starting {step_name}...")
    start_time = time.time()
    
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True)
        
        if result.returncode == 0:
            elapsed = time.time() - start_time
            logger.info(f"✅ {step_name} completed successfully in {elapsed:.1f}s")
            
            # Check if expected files were created
            if check_files:
                for file_path in check_files:
                    if not os.path.exists(file_path):
                        logger.warning(f"⚠️ Expected file not found: {file_path}")
                        return False
                    else:
                        logger.info(f"📁 Created: {file_path}")
            
            return True
        else:
            logger.error(f"❌ {step_name} failed!")
            logger.error(f"Command: {command}")
            logger.error(f"Error: {result.stderr}")
            return False
            
    except Exception as e:
        logger.error(f"❌ {step_name} error: {e}")
        return False

def check_prerequisites():
    """Kiểm tra các điều kiện tiên quyết"""
    logger.info("🔍 Checking prerequisites...")
    
    # Check data directory
    if not os.path.exists("./data"):
        logger.error("❌ Data directory not found!")
        return False
    
    # Check if data files exist
    data_files = list(Path("./data").glob("*.txt"))
    if not data_files:
        logger.error("❌ No .txt files found in data directory!")
        return False
    
    logger.info(f"✅ Found {len(data_files)} data files")
    
    # Check Python dependencies
    required_packages = ['torch', 'transformers', 'peft', 'requests']
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        logger.warning(f"⚠️ Missing packages: {missing_packages}")
        logger.info("💡 Install with: pip install torch transformers peft requests")
        return False
    
    logger.info("✅ All prerequisites met")
    return True

def main():
    """Main pipeline function"""
    logger.info("🎯 Starting Full Fine-tuning Pipeline")
    logger.info("=" * 60)
    
    # Check prerequisites
    if not check_prerequisites():
        logger.error("❌ Prerequisites not met!")
        return False
    
    # Step 1: Generate QnA data
    logger.info("📊 STEP 1: Generate QnA Data")
    logger.info("-" * 40)
    
    if not run_step(
        "QnA Data Generation",
        "python generate_qna_data.py",
        check_files=["generated_qna_data.json", "training_data.json"]
    ):
        logger.error("❌ Failed to generate QnA data!")
        return False
    
    # Step 2: Fine-tune with LoRA
    logger.info("🧠 STEP 2: LoRA Fine-tuning")
    logger.info("-" * 40)
    
    if not run_step(
        "LoRA Fine-tuning",
        "python qwen3_lora_finetune.py",
        check_files=["./qwen3_lora_adapters", "./qwen3_ams_merged"]
    ):
        logger.error("❌ Fine-tuning failed!")
        return False
    
    # Step 3: Convert to GGUF and deploy
    logger.info("🔄 STEP 3: Convert to GGUF and Deploy")
    logger.info("-" * 40)
    
    # Check if merged model exists
    merged_model_path = "./qwen3_ams_merged"
    if not os.path.exists(merged_model_path):
        logger.error(f"❌ Merged model not found: {merged_model_path}")
        return False
    
    if not run_step(
        "GGUF Conversion and Ollama Deployment",
        f"python convert_to_gguf.py --model-path {merged_model_path} --quantization q4_0 --model-name qwen3-ams-lora",
        check_files=["qwen3_ams_merged.gguf"]
    ):
        logger.error("❌ GGUF conversion failed!")
        return False
    
    # Final summary
    logger.info("🎉 PIPELINE COMPLETED SUCCESSFULLY!")
    logger.info("=" * 60)
    logger.info("📋 Summary:")
    logger.info("  ✅ Generated QnA data from AMS documents")
    logger.info("  ✅ Fine-tuned Qwen3-1.7B with LoRA")
    logger.info("  ✅ Converted to GGUF format")
    logger.info("  ✅ Deployed to Ollama as 'qwen3-ams-lora'")
    logger.info("")
    logger.info("🚀 Usage:")
    logger.info("  ollama run qwen3-ams-lora 'Công ty AMS có địa chỉ ở đâu?'")
    logger.info("")
    logger.info("📁 Generated files:")
    logger.info("  - generated_qna_data.json: Detailed QnA data")
    logger.info("  - training_data.json: Training format data")
    logger.info("  - qwen3_lora_adapters/: LoRA adapter weights")
    logger.info("  - qwen3_ams_merged/: Merged model")
    logger.info("  - qwen3_ams_merged.gguf: GGUF format model")
    logger.info("  - full_pipeline.log: Complete log")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
