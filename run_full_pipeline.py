#!/usr/bin/env python3
"""
Qwen3 LoRA Fine-tuning Production Pipeline

Bước 1: Generate QnA data từ AMS documents sử dụng Ollama API
Bước 2: Fine-tune Qwen3-1.7B với LoRA
Bước 3: <PERSON>vert sang GGUF và deploy lên Ollama
Bước 4: Test và validate model
"""
import os
import sys
import logging
import subprocess
import time
import json
import requests
from pathlib import Path

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('full_pipeline.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

def run_step(step_name, command, check_files=None):
    """Chạy một bước trong pipeline"""
    logger.info(f"🚀 Starting {step_name}...")
    start_time = time.time()

    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True)

        if result.returncode == 0:
            elapsed = time.time() - start_time
            logger.info(f"✅ {step_name} completed successfully in {elapsed:.1f}s")

            # Check if expected files were created
            if check_files:
                for file_path in check_files:
                    if not os.path.exists(file_path):
                        logger.warning(f"⚠️ Expected file not found: {file_path}")
                        return False
                    else:
                        logger.info(f"📁 Created: {file_path}")

            return True
        else:
            logger.error(f"❌ {step_name} failed!")
            logger.error(f"Command: {command}")
            logger.error(f"Error: {result.stderr}")
            return False

    except Exception as e:
        logger.error(f"❌ {step_name} error: {e}")
        return False

def check_prerequisites():
    """Kiểm tra các điều kiện tiên quyết"""
    logger.info("🔍 Checking prerequisites...")

    # Check data directory
    if not os.path.exists("./data"):
        logger.error("❌ Data directory not found!")
        return False

    # Check if data files exist
    data_files = list(Path("./data").glob("*.txt"))
    if not data_files:
        logger.error("❌ No .txt files found in data directory!")
        return False

    logger.info(f"✅ Found {len(data_files)} data files")

    # Check Python dependencies
    required_packages = ['torch', 'transformers', 'peft', 'requests']
    missing_packages = []

    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)

    if missing_packages:
        logger.warning(f"⚠️ Missing packages: {missing_packages}")
        logger.info("💡 Install with: pip install torch transformers peft requests")
        return False

    logger.info("✅ All prerequisites met")
    return True

def test_model_deployment(model_name="qwen3-ams-lora", ollama_url="http://localhost:11434"):
    """Test model deployment và inference"""
    logger.info("🧪 Testing model deployment...")

    test_questions = [
        "Công ty AMS có địa chỉ ở đâu?",
        "Thời gian thử việc tại AMS là bao lâu?",
        "Giờ làm việc của AMS như thế nào?"
    ]

    try:
        # Check if model exists
        response = requests.get(f"{ollama_url}/api/tags", timeout=10)
        if response.status_code != 200:
            logger.error(f"❌ Cannot connect to Ollama at {ollama_url}")
            return False

        models = response.json().get('models', [])
        model_names = [model['name'] for model in models]

        if model_name not in model_names:
            logger.error(f"❌ Model {model_name} not found in Ollama")
            return False

        logger.info(f"✅ Model {model_name} found in Ollama")

        # Test inference
        success_count = 0
        for i, question in enumerate(test_questions, 1):
            try:
                logger.info(f"Testing question {i}: {question}")

                response = requests.post(
                    f"{ollama_url}/api/generate",
                    json={
                        "model": model_name,
                        "prompt": question,
                        "stream": False,
                        "options": {"temperature": 0.3, "top_p": 0.9, "num_predict": 150}
                    },
                    timeout=60
                )

                if response.status_code == 200:
                    result = response.json()
                    answer = result.get('response', '').strip()
                    logger.info(f"✅ Answer {i}: {answer[:100]}...")
                    success_count += 1
                else:
                    logger.error(f"❌ API error for question {i}: {response.status_code}")

            except Exception as e:
                logger.error(f"❌ Error testing question {i}: {e}")

        success_rate = success_count / len(test_questions) * 100
        logger.info(f"📊 Test success rate: {success_rate:.1f}% ({success_count}/{len(test_questions)})")

        return success_rate >= 66.7  # At least 2/3 tests should pass

    except Exception as e:
        logger.error(f"❌ Model testing failed: {e}")
        return False

def generate_final_report():
    """Generate final deployment report"""
    logger.info("📄 Generating final report...")

    # Collect file information
    files_info = {}
    expected_files = [
        "generated_qna_data.json",
        "training_data.json",
        "qwen3_lora_adapters",
        "qwen3_ams_merged",
        "qwen3_ams_merged.gguf"
    ]

    for file_path in expected_files:
        if os.path.exists(file_path):
            if os.path.isfile(file_path):
                size_mb = os.path.getsize(file_path) / (1024 * 1024)
                files_info[file_path] = f"{size_mb:.1f} MB"
            else:
                files_info[file_path] = "Directory"
        else:
            files_info[file_path] = "Not found"

    # Load training data stats
    training_stats = {}
    try:
        if os.path.exists("generated_qna_data.json"):
            with open("generated_qna_data.json", 'r', encoding='utf-8') as f:
                qna_data = json.load(f)
            training_stats['total_pairs'] = len(qna_data)

            sources = {}
            for item in qna_data:
                source = item.get('source_file', 'unknown')
                sources[source] = sources.get(source, 0) + 1
            training_stats['sources'] = sources
    except:
        training_stats = {'error': 'Could not load training data'}

    # Generate report
    report = f"""# Qwen3 LoRA Fine-tuning Pipeline Report

Generated: {time.strftime('%Y-%m-%d %H:%M:%S')}

## Pipeline Status: ✅ COMPLETED

## Generated Files
"""

    for file_path, info in files_info.items():
        status = "✅" if info != "Not found" else "❌"
        report += f"- {status} {file_path}: {info}\n"

    report += f"\n## Training Data Statistics\n"
    if 'total_pairs' in training_stats:
        report += f"- Total QnA pairs: {training_stats['total_pairs']}\n"
        report += f"- Source files:\n"
        for source, count in training_stats['sources'].items():
            report += f"  - {source}: {count} pairs\n"
    else:
        report += f"- Error: {training_stats.get('error', 'Unknown')}\n"

    report += f"""\n## Model Information
- Base Model: Qwen/Qwen3-1.7B
- Fine-tuning Method: LoRA (Low-Rank Adaptation)
- Output Format: GGUF (Quantized Q4_0)
- Deployment: Ollama (qwen3-ams-lora)

## Usage
```bash
ollama run qwen3-ams-lora "Công ty AMS có địa chỉ ở đâu?"
```

## Benefits
- 🚀 10-100x fewer trainable parameters with LoRA
- 💾 50-60% smaller model size with GGUF
- ⚡ 2-3x faster inference
- 🔧 Easy deployment with Ollama
"""

    # Save report
    with open('pipeline_report.md', 'w', encoding='utf-8') as f:
        f.write(report)

    logger.info("✅ Report saved to: pipeline_report.md")
    return True

def main():
    """Main pipeline function"""
    logger.info("🎯 Qwen3 LoRA Fine-tuning Production Pipeline")
    logger.info("=" * 60)

    # Check prerequisites
    if not check_prerequisites():
        logger.error("❌ Prerequisites not met!")
        return False

    # Step 1: Generate QnA data (with caching)
    logger.info("📊 STEP 1: Generate QnA Data (Smart Caching)")
    logger.info("-" * 40)

    # Check if QnA data already exists
    if os.path.exists("generated_qna_data.json") and os.path.exists("training_data.json"):
        logger.info("📦 QnA data files found, checking if update needed...")

    if not run_step(
        "QnA Data Generation with Caching",
        "python generate_qna_data.py",
        check_files=["generated_qna_data.json", "training_data.json"]
    ):
        logger.error("❌ Failed to generate QnA data!")
        return False

    # Step 2: Fine-tune with LoRA
    logger.info("🧠 STEP 2: LoRA Fine-tuning")
    logger.info("-" * 40)

    if not run_step(
        "LoRA Fine-tuning",
        "python qwen3_lora_finetune.py",
        check_files=["./qwen3_lora_adapters", "./qwen3_ams_merged"]
    ):
        logger.error("❌ Fine-tuning failed!")
        return False

    # Step 3: Convert to GGUF and deploy
    logger.info("🔄 STEP 3: Convert to GGUF and Deploy")
    logger.info("-" * 40)

    merged_model_path = "./qwen3_ams_merged"
    if not os.path.exists(merged_model_path):
        logger.error(f"❌ Merged model not found: {merged_model_path}")
        return False

    if not run_step(
        "GGUF Conversion and Ollama Deployment",
        f"python convert_to_gguf.py --model-path {merged_model_path} --quantization q4_0 --model-name qwen3-ams-lora",
        check_files=["qwen3_ams_merged.gguf"]
    ):
        logger.error("❌ GGUF conversion failed!")
        return False

    # Step 4: Test deployment
    logger.info("🧪 STEP 4: Test Model Deployment")
    logger.info("-" * 40)

    test_success = test_model_deployment()
    if test_success:
        logger.info("✅ Model testing passed!")
    else:
        logger.warning("⚠️ Model testing failed, but pipeline completed")

    # Step 5: Generate final report
    logger.info("📄 STEP 5: Generate Final Report")
    logger.info("-" * 40)

    generate_final_report()

    # Final summary
    logger.info("🎉 PIPELINE COMPLETED SUCCESSFULLY!")
    logger.info("=" * 60)
    logger.info("📋 Summary:")
    logger.info("  ✅ Generated QnA data from AMS documents")
    logger.info("  ✅ Fine-tuned Qwen3-1.7B with LoRA")
    logger.info("  ✅ Converted to GGUF format")
    logger.info("  ✅ Deployed to Ollama as 'qwen3-ams-lora'")
    logger.info(f"  {'✅' if test_success else '⚠️'} Model testing {'passed' if test_success else 'had issues'}")
    logger.info("")
    logger.info("🚀 Usage:")
    logger.info("  ollama run qwen3-ams-lora 'Công ty AMS có địa chỉ ở đâu?'")
    logger.info("")
    logger.info("📁 Generated files:")
    logger.info("  - generated_qna_data.json: Detailed QnA data")
    logger.info("  - training_data.json: Training format data")
    logger.info("  - qwen3_lora_adapters/: LoRA adapter weights")
    logger.info("  - qwen3_ams_merged/: Merged model")
    logger.info("  - qwen3_ams_merged.gguf: GGUF format model")
    logger.info("  - pipeline_report.md: Detailed report")
    logger.info("  - full_pipeline.log: Complete log")

    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
