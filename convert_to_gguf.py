#!/usr/bin/env python3
"""
Script chuyển đổi model.safetensors sau fine-tuning thành GGUF format
Hỗ trợ chạy trê<PERSON>
"""
import os
import sys
import logging
import subprocess
import shutil
from pathlib import Path

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def check_dependencies():
    """Kiểm tra các dependencies cần thiết"""
    logger.info("🔍 Checking dependencies...")
    
    # Check if llama.cpp is available
    try:
        result = subprocess.run(['python', '-c', 'import llama_cpp'], 
                              capture_output=True, text=True)
        if result.returncode != 0:
            logger.warning("⚠️ llama-cpp-python not found, will try to install...")
            return install_dependencies()
    except:
        logger.warning("⚠️ llama-cpp-python not found, will try to install...")
        return install_dependencies()
    
    # Check if we have convert script from llama.cpp
    convert_script_paths = [
        "/opt/homebrew/bin/convert.py",  # Homebrew on macOS
        "/usr/local/bin/convert.py",    # Standard install
        "./llama.cpp/convert.py",       # Local clone
        "convert.py"                    # Current directory
    ]
    
    for path in convert_script_paths:
        if os.path.exists(path):
            logger.info(f"✅ Found convert script at: {path}")
            return path
    
    logger.warning("⚠️ convert.py not found, will try to download llama.cpp...")
    return setup_llama_cpp()

def install_dependencies():
    """Cài đặt dependencies"""
    try:
        logger.info("📦 Installing llama-cpp-python...")
        subprocess.run([sys.executable, '-m', 'pip', 'install', 'llama-cpp-python'], 
                      check=True)
        logger.info("✅ llama-cpp-python installed successfully")
        return setup_llama_cpp()
    except subprocess.CalledProcessError as e:
        logger.error(f"❌ Failed to install llama-cpp-python: {e}")
        return False

def setup_llama_cpp():
    """Setup llama.cpp repository"""
    try:
        if not os.path.exists("llama.cpp"):
            logger.info("📥 Cloning llama.cpp repository...")
            subprocess.run([
                'git', 'clone', 'https://github.com/ggerganov/llama.cpp.git'
            ], check=True)
        
        convert_script = "./llama.cpp/convert.py"
        if os.path.exists(convert_script):
            logger.info(f"✅ Found convert script at: {convert_script}")
            return convert_script
        else:
            logger.error("❌ convert.py not found in llama.cpp repository")
            return False
            
    except subprocess.CalledProcessError as e:
        logger.error(f"❌ Failed to setup llama.cpp: {e}")
        return False

def convert_to_gguf(model_path, output_path=None, quantization="q4_0"):
    """Chuyển đổi model sang GGUF format"""
    
    if not os.path.exists(model_path):
        logger.error(f"❌ Model path not found: {model_path}")
        return False
    
    # Check for convert script
    convert_script = check_dependencies()
    if not convert_script:
        logger.error("❌ Cannot find or setup conversion tools")
        return False
    
    # Set output path
    if output_path is None:
        model_name = os.path.basename(model_path.rstrip('/'))
        output_path = f"{model_name}.gguf"
    
    try:
        logger.info(f"🔄 Converting {model_path} to GGUF format...")
        
        # Step 1: Convert to GGUF (FP16)
        temp_gguf = f"{output_path}.tmp"
        cmd = [
            'python', convert_script,
            model_path,
            '--outfile', temp_gguf,
            '--outtype', 'f16'
        ]
        
        logger.info(f"Running: {' '.join(cmd)}")
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode != 0:
            logger.error(f"❌ Conversion failed: {result.stderr}")
            return False
        
        logger.info("✅ Successfully converted to FP16 GGUF")
        
        # Step 2: Quantize if requested
        if quantization and quantization != "f16":
            logger.info(f"🔄 Quantizing to {quantization}...")
            
            # Find quantize binary
            quantize_paths = [
                "./llama.cpp/quantize",
                "/opt/homebrew/bin/quantize",
                "/usr/local/bin/quantize",
                "quantize"
            ]
            
            quantize_bin = None
            for path in quantize_paths:
                if os.path.exists(path) or shutil.which(path):
                    quantize_bin = path
                    break
            
            if not quantize_bin:
                logger.warning("⚠️ Quantize binary not found, building from source...")
                # Try to build quantize
                try:
                    subprocess.run(['make', '-C', 'llama.cpp', 'quantize'], check=True)
                    quantize_bin = "./llama.cpp/quantize"
                except:
                    logger.warning("⚠️ Could not build quantize, using FP16 version")
                    shutil.move(temp_gguf, output_path)
                    return output_path
            
            # Quantize the model
            cmd = [quantize_bin, temp_gguf, output_path, quantization]
            logger.info(f"Running: {' '.join(cmd)}")
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode == 0:
                logger.info(f"✅ Successfully quantized to {quantization}")
                os.remove(temp_gguf)  # Remove temp file
            else:
                logger.warning(f"⚠️ Quantization failed, using FP16: {result.stderr}")
                shutil.move(temp_gguf, output_path)
        else:
            shutil.move(temp_gguf, output_path)
        
        # Verify output file
        if os.path.exists(output_path):
            file_size = os.path.getsize(output_path) / (1024 * 1024)  # MB
            logger.info(f"✅ GGUF model created: {output_path} ({file_size:.1f} MB)")
            return output_path
        else:
            logger.error("❌ Output file not created")
            return False
            
    except Exception as e:
        logger.error(f"❌ Conversion error: {e}")
        return False

def create_ollama_modelfile(gguf_path, model_name="qwen3-ams-lora"):
    """Tạo Modelfile cho Ollama"""
    
    modelfile_content = f"""FROM {gguf_path}

TEMPLATE \"\"\"Câu hỏi: {{{{ .Prompt }}}}
Trả lời: \"\"\"

PARAMETER temperature 0.3
PARAMETER top_p 0.9
PARAMETER top_k 40
PARAMETER repeat_penalty 1.1
PARAMETER stop "Câu hỏi:"
PARAMETER stop "\\n\\n"

SYSTEM \"\"\"Bạn là trợ lý AI của công ty AMS, được fine-tune với LoRA để trả lời chính xác các câu hỏi về quy định công ty bằng tiếng Việt. Hãy trả lời ngắn gọn, chính xác và hữu ích.\"\"\"
"""
    
    modelfile_path = f"Modelfile.{model_name}"
    try:
        with open(modelfile_path, 'w', encoding='utf-8') as f:
            f.write(modelfile_content)
        logger.info(f"✅ Created Modelfile: {modelfile_path}")
        return modelfile_path
    except Exception as e:
        logger.error(f"❌ Failed to create Modelfile: {e}")
        return False

def deploy_to_ollama(gguf_path, model_name="qwen3-ams-lora", ollama_host="localhost:11434"):
    """Deploy model to Ollama"""
    
    # Create Modelfile
    modelfile_path = create_ollama_modelfile(gguf_path, model_name)
    if not modelfile_path:
        return False
    
    try:
        # Create model in Ollama
        logger.info(f"🚀 Deploying to Ollama as {model_name}...")
        cmd = ['ollama', 'create', model_name, '-f', modelfile_path]
        
        # Set OLLAMA_HOST if different from default
        env = os.environ.copy()
        if ollama_host != "localhost:11434":
            env['OLLAMA_HOST'] = ollama_host
        
        logger.info(f"Running: {' '.join(cmd)}")
        result = subprocess.run(cmd, capture_output=True, text=True, env=env)
        
        if result.returncode == 0:
            logger.info(f"✅ Successfully deployed model: {model_name}")
            
            # Test the model
            logger.info("🧪 Testing deployed model...")
            test_cmd = ['ollama', 'run', model_name, 'Công ty AMS có địa chỉ ở đâu?']
            test_result = subprocess.run(test_cmd, capture_output=True, text=True, env=env, timeout=30)
            
            if test_result.returncode == 0:
                logger.info(f"🎉 Test successful! Response: {test_result.stdout.strip()}")
            else:
                logger.warning(f"⚠️ Test failed: {test_result.stderr}")
            
            return model_name
        else:
            logger.error(f"❌ Failed to deploy to Ollama: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        logger.warning("⚠️ Test timeout, but model might be deployed successfully")
        return model_name
    except Exception as e:
        logger.error(f"❌ Deployment error: {e}")
        return False

def main():
    """Main function"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Convert fine-tuned model to GGUF and deploy to Ollama")
    parser.add_argument("--model-path", required=True, help="Path to fine-tuned model directory")
    parser.add_argument("--output", help="Output GGUF file path")
    parser.add_argument("--quantization", default="q4_0", 
                       choices=["f16", "q8_0", "q4_0", "q4_1", "q5_0", "q5_1", "q2_k", "q3_k", "q4_k", "q5_k", "q6_k"],
                       help="Quantization type (default: q4_0)")
    parser.add_argument("--model-name", default="qwen3-ams-lora", help="Ollama model name")
    parser.add_argument("--ollama-host", default="localhost:11434", help="Ollama host")
    parser.add_argument("--no-deploy", action="store_true", help="Don't deploy to Ollama, just convert")
    
    args = parser.parse_args()
    
    logger.info("🚀 Starting model conversion to GGUF...")
    
    # Convert to GGUF
    gguf_path = convert_to_gguf(args.model_path, args.output, args.quantization)
    if not gguf_path:
        logger.error("❌ Conversion failed!")
        return False
    
    if args.no_deploy:
        logger.info(f"✅ Conversion complete: {gguf_path}")
        logger.info("💡 To deploy to Ollama manually:")
        logger.info(f"   ollama create {args.model_name} -f Modelfile.{args.model_name}")
        return True
    
    # Deploy to Ollama
    deployed_model = deploy_to_ollama(gguf_path, args.model_name, args.ollama_host)
    if deployed_model:
        logger.info("🎉 Conversion and deployment complete!")
        logger.info(f"📝 Usage: ollama run {deployed_model} 'Your question about AMS'")
        return True
    else:
        logger.error("❌ Deployment failed!")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
