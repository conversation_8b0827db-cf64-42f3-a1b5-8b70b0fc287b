#!/bin/bash
# Qwen3 LoRA Fine-tuning Pipeline Launcher
# Simplified production-ready script

set -e  # Exit on any error

echo "🎯 Qwen3 LoRA Fine-tuning Production Pipeline"
echo "=============================================="

# Check if Python is available
if ! command -v python3 &> /dev/null; then
    echo "❌ Python3 not found. Please install Python 3.8+"
    exit 1
fi

# Check if data directory exists
if [ ! -d "./data" ]; then
    echo "❌ Data directory not found. Please ensure ./data/ contains AMS .txt files"
    exit 1
fi

# Count data files
data_files=$(find ./data -name "*.txt" | wc -l)
if [ "$data_files" -eq 0 ]; then
    echo "❌ No .txt files found in ./data/ directory"
    exit 1
fi

echo "✅ Found $data_files data files in ./data/"

# Check if virtual environment exists
if [ ! -d "./venv" ]; then
    echo "📦 Creating Python virtual environment..."
    python3 -m venv venv
fi

# Activate virtual environment
source venv/bin/activate

# Install/upgrade dependencies
echo "📦 Installing dependencies..."
pip install --upgrade pip
pip install torch transformers peft requests datasets accelerate pynvml

# Check GPU availability
echo "🔍 Checking GPU availability..."
if command -v nvidia-smi &> /dev/null; then
    nvidia-smi --query-gpu=name,memory.total,memory.free --format=csv,noheader
else
    echo "⚠️ nvidia-smi not found. GPU may not be available."
fi

# Run the pipeline
echo "🚀 Starting pipeline..."
python run_full_pipeline.py

# Check results
if [ $? -eq 0 ]; then
    echo ""
    echo "🎉 Pipeline completed successfully!"
    echo ""
    echo "📁 Generated files:"
    ls -la *.json *.gguf *.md 2>/dev/null || echo "  No output files found"
    echo ""
    echo "🚀 To use the model:"
    echo "  ollama run qwen3-ams-lora 'Công ty AMS có địa chỉ ở đâu?'"
else
    echo "❌ Pipeline failed. Check full_pipeline.log for details."
    exit 1
fi
