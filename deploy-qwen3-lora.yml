---
- name: <PERSON><PERSON> Fine-tuning Qwen3-1.7B with AMS Data on Tesla P40
  hosts: tesla_p40
  become: false
  gather_facts: true

  vars:
    project_dir: /home/<USER>/projects/qwen3_lora_finetune
    venv_dir: "{{ project_dir }}/env"

  tasks:
    - name: Check GPU availability
      shell: nvidia-smi --query-gpu=name,memory.total,memory.free --format=csv,noheader,nounits
      register: gpu_info

    - name: Display GPU information
      debug:
        msg: "GPU detected: {{ gpu_info.stdout }}"

    - name: Create project directory
      file:
        path: "{{ project_dir }}"
        state: directory
        mode: '0755'

    - name: Copy AMS data files to server
      copy:
        src: "./data/"
        dest: "{{ project_dir }}/data/"
        mode: '0644'

    - name: Create Python virtual environment
      shell: python3 -m venv {{ venv_dir }}
      args:
        creates: "{{ venv_dir }}/bin/activate"

    - name: Upgrade pip
      shell: "{{ venv_dir }}/bin/pip install --upgrade pip"

    - name: Install PyTorch with CUDA support
      shell: "{{ venv_dir }}/bin/pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118"

    - name: Install LoRA and fine-tuning dependencies
      shell: "{{ venv_dir }}/bin/pip install {{ item }}"
      loop:
        - "transformers>=4.35.0"
        - "datasets"
        - "pandas"
        - "numpy"
        - "accelerate"
        - "peft>=0.6.0"  # Essential for LoRA
        - "bitsandbytes"
        - "pynvml"
        - "scipy"

    - name: Copy LoRA fine-tuning script
      copy:
        dest: "{{ project_dir }}/qwen3_lora_finetune.py"
        src: "qwen3_lora_finetune.py"
        mode: '0755'

    - name: Start GPU monitoring for LoRA training
      shell: "cd {{ project_dir }} && nohup {{ venv_dir }}/bin/python3 -c '
        import pynvml, time, os;
        pynvml.nvmlInit();
        d = pynvml.nvmlDeviceGetHandleByIndex(0);
        with open(\"gpu_lora_monitor.log\", \"w\") as f:
          while True:
            try:
              u = pynvml.nvmlDeviceGetUtilizationRates(d);
              m = pynvml.nvmlDeviceGetMemoryInfo(d);
              log_line = f\"{time.strftime(\"%Y-%m-%d %H:%M:%S\")} | GPU: {u.gpu}% | Mem: {m.used/1024/1024:.1f}/{m.total/1024/1024:.1f} MB | LoRA Training\";
              print(log_line);
              f.write(log_line + \"\\n\");
              f.flush();
              time.sleep(5)
            except:
              break
        ' > /dev/null 2>&1 &"
      async: 1800
      poll: 0

    - name: Run LoRA fine-tuning
      shell: "cd {{ project_dir }} && {{ venv_dir }}/bin/python3 qwen3_lora_finetune.py"
      register: lora_training_result
      async: 1800  # 30 min timeout (LoRA is much faster)
      poll: 15     # Check every 15 seconds

    - name: Display LoRA training output
      debug:
        msg: "{{ lora_training_result.stdout }}"
      when: lora_training_result.stdout is defined

    - name: Display LoRA training errors
      debug:
        msg: "{{ lora_training_result.stderr }}"
      when: lora_training_result.stderr is defined and lora_training_result.stderr != ""

    - name: Check if LoRA adapters were created
      stat:
        path: "{{ project_dir }}/qwen3_lora_adapters"
      register: lora_check

    - name: Check if merged model was created
      stat:
        path: "{{ project_dir }}/qwen3_ams_merged"
      register: merged_check

    - name: Display LoRA training status
      debug:
        msg: |
          LoRA Adapters: {{ 'Created' if lora_check.stat.exists else 'Failed' }}
          Merged Model: {{ 'Created' if merged_check.stat.exists else 'Failed' }}

    - name: Get LoRA adapter size
      shell: "du -sh {{ project_dir }}/qwen3_lora_adapters"
      register: lora_size
      when: lora_check.stat.exists
      ignore_errors: true

    - name: Get merged model size
      shell: "du -sh {{ project_dir }}/qwen3_ams_merged"
      register: merged_size
      when: merged_check.stat.exists
      ignore_errors: true

    - name: Display model sizes
      debug:
        msg: |
          LoRA Adapters Size: {{ lora_size.stdout if lora_check.stat.exists and lora_size.stdout is defined else 'N/A' }}
          Merged Model Size: {{ merged_size.stdout if merged_check.stat.exists and merged_size.stdout is defined else 'N/A' }}

    - name: Check if Ollama LoRA model was created
      shell: "ollama list | grep qwen3-ams-lora"
      register: ollama_lora_check
      ignore_errors: true

    - name: Display Ollama LoRA model status
      debug:
        msg: "{{ 'Ollama LoRA model deployed successfully!' if ollama_lora_check.rc == 0 else 'Ollama LoRA deployment failed!' }}"

    - name: Test LoRA fine-tuned Ollama model
      shell: 'ollama run qwen3-ams-lora "Công ty AMS có địa chỉ ở đâu?"'
      register: lora_test_result
      when: ollama_lora_check.rc == 0
      ignore_errors: true

    - name: Display LoRA test result
      debug:
        msg: "LoRA Test result: {{ lora_test_result.stdout }}"
      when: lora_test_result.stdout is defined

    - name: Stop GPU monitoring
      shell: "pkill -f 'pynvml'"
      ignore_errors: true

    - name: Show LoRA GPU monitoring log
      shell: "tail -20 {{ project_dir }}/gpu_lora_monitor.log"
      register: lora_gpu_log
      ignore_errors: true

    - name: Display LoRA GPU monitoring summary
      debug:
        msg: "LoRA GPU monitoring log: {{ lora_gpu_log.stdout }}"
      when: lora_gpu_log.stdout is defined

    - name: Create LoRA deployment summary
      copy:
        dest: "{{ project_dir }}/lora_deployment_summary.txt"
        content: |
          LoRA Fine-tuning Deployment Summary
          ==================================
          
          Deployment Date: {{ ansible_date_time.iso8601 }}
          Server: {{ inventory_hostname }}
          GPU: {{ gpu_info.stdout }}
          Project Directory: {{ project_dir }}
          
          LoRA Training Status: {{ 'SUCCESS' if lora_check.stat.exists else 'FAILED' }}
          Model Merging Status: {{ 'SUCCESS' if merged_check.stat.exists else 'FAILED' }}
          Ollama Deployment: {{ 'SUCCESS' if ollama_lora_check.rc == 0 else 'FAILED' }}
          
          LoRA Adapter Size: {{ lora_size.stdout if lora_check.stat.exists and lora_size.stdout is defined else 'N/A' }}
          Merged Model Size: {{ merged_size.stdout if merged_check.stat.exists and merged_size.stdout is defined else 'N/A' }}
          
          LoRA Configuration:
          - Rank (r): 16
          - Alpha: 32
          - Dropout: 0.1
          - Target Modules: q_proj, k_proj, v_proj, o_proj, gate_proj, up_proj, down_proj
          - Training Method: Parameter-Efficient Fine-Tuning
          
          LoRA Benefits:
          - 10-100x fewer trainable parameters
          - 3-5x faster training
          - 50-70% less VRAM usage
          - Better generalization
          - Easy to merge/unmerge
          
          Model Details:
          - Base Model: Qwen/Qwen2-1.5B
          - Fine-tuned with AMS employee handbook data using LoRA
          - Deployed as: qwen3-ams-lora
          
          Usage:
          ollama run qwen3-ams-lora "Your question about AMS"
          
          Test Results:
          {{ lora_test_result.stdout if lora_test_result.stdout is defined else 'No test results available' }}
        mode: '0644'

    - name: Display final LoRA summary
      debug:
        msg: |
          🎉 LoRA Fine-tuning Completed!
          
          ✅ LoRA Adapters: {{ 'Created' if lora_check.stat.exists else 'Failed' }}
          ✅ Merged Model: {{ 'Created' if merged_check.stat.exists else 'Failed' }}
          ✅ Ollama Deployment: {{ 'Success' if ollama_lora_check.rc == 0 else 'Failed' }}
          ✅ GPU: {{ gpu_info.stdout }}
          
          📊 Efficiency Gains:
          - LoRA Adapter Size: {{ lora_size.stdout if lora_check.stat.exists and lora_size.stdout is defined else 'N/A' }}
          - Training Time: ~10-15 minutes (vs 1-2 hours full fine-tuning)
          - VRAM Usage: ~6-8GB (vs 15-20GB full fine-tuning)
          
          To use the LoRA fine-tuned model:
          ollama run qwen3-ams-lora "Câu hỏi về AMS"
